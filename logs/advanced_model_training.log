2025-07-14 08:00:37,779 - INFO - 高级空调负荷模型训练器初始化完成
2025-07-14 08:00:37,779 - INFO - 加载数据文件: data/processed/final_wide_table_20250714_075137.csv
2025-07-14 08:00:39,231 - INFO - 已加载 250000 行数据...
2025-07-14 08:00:39,912 - INFO - 数据加载完成，形状: (350880, 127)
2025-07-14 08:00:39,934 - INFO - 准备训练数据...
2025-07-14 08:00:39,934 - INFO - 创建目标变量...
2025-07-14 08:00:40,192 - INFO - 使用温度列: Temperature_甘孜
2025-07-14 08:00:40,211 - INFO - 空调负荷统计: 均值=4.99, 标准差=25.92
2025-07-14 08:00:40,234 - INFO - 准备特征数据...
2025-07-14 08:00:40,464 - INFO - 选择了 66 个特征
2025-07-14 08:00:40,464 - INFO - 特征列表: ['PM2_5_甘孜', 'HourlyLoadFactor', 'HourlyReactiveEnergy', 'O3_新都', 'SO2_新都', 'PM2_5_新都', 'Pressure_金堂', 'Humidity_简阳', 'SO2_简阳', 'WindSpeed_V_金堂']...
2025-07-14 08:00:40,630 - ERROR - 高级模型训练失败: Cannot convert [['02:00:00' '02:00:00' '02:00:00' ... '05:00:00' '05:00:00' '05:00:00']
 ['19:00:00' '19:00:00' '19:00:00' ... '13:00:00' '13:00:00' '13:00:00']] to numeric
2025-07-14 08:01:37,171 - INFO - 高级空调负荷模型训练器初始化完成
2025-07-14 08:01:37,171 - INFO - 加载数据文件: data/processed/final_wide_table_20250714_075137.csv
2025-07-14 08:01:38,586 - INFO - 已加载 250000 行数据...
2025-07-14 08:01:39,254 - INFO - 数据加载完成，形状: (350880, 127)
2025-07-14 08:01:39,279 - INFO - 准备训练数据...
2025-07-14 08:01:39,279 - INFO - 创建目标变量...
2025-07-14 08:01:39,500 - INFO - 使用温度列: Temperature_甘孜
2025-07-14 08:01:39,517 - INFO - 空调负荷统计: 均值=5.02, 标准差=26.21
2025-07-14 08:01:39,543 - INFO - 准备特征数据...
2025-07-14 08:01:39,822 - INFO - 选择了 64 个特征
2025-07-14 08:01:39,822 - INFO - 特征列表: ['AQI_新都', 'WindSpeed_V_新都', 'Hour', 'CO_甘孜', 'Pressure_甘孜', 'Humidity_金堂', 'IsWeekend', 'Pressure_简阳', 'NO2_简阳', 'HourlyMaxLoad']...
2025-07-14 08:01:40,144 - INFO - 移除异常值后数据形状: X=(303888, 64), y=(303888,)
2025-07-14 08:01:40,454 - INFO - 数据分割完成:
2025-07-14 08:01:40,454 - INFO -   训练集: (182332, 64)
2025-07-14 08:01:40,454 - INFO -   验证集: (60778, 64)
2025-07-14 08:01:40,454 - INFO -   测试集: (60778, 64)
2025-07-14 08:01:40,460 - INFO - 训练传统机器学习模型...
2025-07-14 08:01:40,461 - INFO - 训练 XGBoost 模型...
2025-07-14 08:01:42,003 - INFO - XGBoost - 验证集 R²: 0.0723, RMSE: 0.0542
2025-07-14 08:01:42,003 - INFO - 训练 LightGBM 模型...
2025-07-14 08:01:44,149 - INFO - LightGBM - 验证集 R²: 0.0747, RMSE: 0.0541
2025-07-14 08:01:44,149 - INFO - 训练 RandomForest 模型...
2025-07-14 08:01:58,288 - INFO - RandomForest - 验证集 R²: 0.0772, RMSE: 0.0540
2025-07-14 08:01:58,288 - INFO - 训练 Ridge 模型...
2025-07-14 08:01:58,336 - INFO - Ridge - 验证集 R²: 0.0385, RMSE: 0.0552
2025-07-14 08:01:58,336 - INFO - 训练 ElasticNet 模型...
2025-07-14 08:01:58,416 - INFO - ElasticNet - 验证集 R²: -0.0001, RMSE: 0.0562
2025-07-14 08:01:58,416 - INFO - 训练深度学习模型...
2025-07-14 08:01:58,417 - INFO - 训练神经网络...
