# 空调负荷柔性调控能力分析系统

## 项目概述

本项目是一个电力负荷分析系统，通过整合电力负荷与气象数据，识别空调负荷、评估其柔性调控能力，并生成相应分析结果。

## 环境配置

### 1. 环境要求
- Python 3.9+
- Conda 环境管理

### 2. 快速启动

```bash
# 1. 激活conda环境
export PATH="/opt/miniconda3/bin:$PATH"
conda activate ac_load_analysis

# 2. 启动Jupyter Lab
./start_jupyter.sh
```

或者直接运行：
```bash
export PATH="/opt/miniconda3/bin:$PATH"
conda activate ac_load_analysis
jupyter lab --no-browser --port=8888
```

然后在浏览器中访问：http://localhost:8888

## 项目结构

```
ac_load/
├── 天气数据/                    # 原始天气数据
│   ├── 双流23.xls
│   ├── 双流24.xls
│   └── ...
├── complete_load_analysis_data.csv  # 负荷数据
├── 技术方案.md                  # 技术方案文档
├── 注释.xlsx                    # 数据注释文件
├── requirements.txt             # Python依赖包
├── setup_env.py                 # 环境配置脚本
├── start_jupyter.sh             # Jupyter启动脚本
├── data/                        # 数据目录
│   ├── raw/                     # 原始数据
│   ├── processed/               # 处理后的数据
│   └── interim/                 # 中间数据
├── notebooks/                   # Jupyter notebooks
│   └── 01_数据探索与整理.ipynb
├── src/                         # 源代码
│   ├── data/                    # 数据处理模块
│   ├── features/                # 特征工程模块
│   ├── models/                  # 模型模块
│   └── visualization/           # 可视化模块
├── models/                      # 模型文件
├── results/                     # 分析结果
├── logs/                        # 日志文件
└── config/                      # 配置文件
```

## 数据文件说明

### 负荷数据
- **文件**: `complete_load_analysis_data.csv`
- **描述**: 包含电力负荷数据，时间粒度从小时到周级

### 天气数据
- **目录**: `天气数据/`
- **格式**: Excel文件 (.xls/.xlsx)
- **内容**: 各区域2023-2025年的气象数据
- **区域**: 双流、大邑、崇州、彭州、成华、攀枝花、新津、新都、武侯、温江、甘孜、简阳、蒲江、邛崃、郫都、都江堰、金堂县、金牛、锦江、青白江、青羊区、龙泉驿等

## 使用指南

### 1. 数据探索
打开 `notebooks/01_数据探索与整理.ipynb` 开始数据探索：
- 了解数据结构
- 评估数据质量
- 识别数据问题
- 制定预处理策略

### 2. 数据预处理
- 数据清洗（缺失值、异常值处理）
- 数据标准化（时间格式、列名规范）
- 数据关联（负荷与天气数据匹配）

### 3. 特征工程
- 时间特征提取
- 温度相关特征
- 负荷特征工程

### 4. 模型构建
- 空调负荷识别模型
- 柔性调控能力评估模型

### 5. 结果分析
- 可视化分析
- 报告生成

## 技术栈

- **数据处理**: pandas, numpy, dask
- **机器学习**: scikit-learn, xgboost, lightgbm
- **可视化**: matplotlib, seaborn, plotly
- **开发环境**: jupyter lab
- **数据存储**: parquet, csv

## 注意事项

1. **数据大小**: 负荷数据文件较大，建议使用分块读取
2. **内存管理**: 处理大数据时注意内存使用
3. **数据备份**: 原始数据请勿修改，处理后的数据保存在 `data/` 目录
4. **环境隔离**: 使用conda环境确保依赖包版本一致

## 开发流程

1. **环境配置** ✅
2. **数据探索** 🔄 (当前阶段)
3. **数据预处理**
4. **特征工程**
5. **模型构建**
6. **结果分析**
7. **报告生成**

## 联系方式

如有问题，请联系项目负责人。 