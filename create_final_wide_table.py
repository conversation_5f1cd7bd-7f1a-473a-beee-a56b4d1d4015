#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
空调负荷柔性调控能力分析系统 - 最终版大宽表生成脚本

基于测试结果的最终修复版本

作者: AI Assistant
创建时间: 2025-07-13
"""

import pandas as pd
import numpy as np
import warnings
import logging
import os
import glob
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional

warnings.filterwarnings('ignore')

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/final_wide_table.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class FinalWideTableCreator:
    """最终版大宽表创建器"""
    
    def __init__(self):
        """初始化"""
        self.load_data = None
        self.weather_data = {}
        self.air_quality_data = {}
        self.wide_table = None
        
        # 创建必要目录
        Path('logs').mkdir(exist_ok=True)
        Path('data/processed').mkdir(parents=True, exist_ok=True)
        
        # 地址到区域的映射表
        self.address_region_mapping = {
            '简阳': ['简阳市', '简阳'],
            '成华': ['成华区', '成华'],
            '双流': ['双流区', '双流'],
            '大邑': ['大邑县', '大邑'],
            '崇州': ['崇州市', '崇州'],
            '彭州': ['彭州市', '彭州'],
            '新津': ['新津区', '新津县', '新津'],
            '新都': ['新都区', '新都'],
            '武侯': ['武侯区', '武侯'],
            '温江': ['温江区', '温江'],
            '甘孜': ['甘孜州', '甘孜藏族自治州', '甘孜'],
            '蒲江': ['蒲江县', '蒲江'],
            '邛崃': ['邛崃市', '邛崃'],
            '郫都': ['郫都区', '郫县', '郫都'],
            '都江堰': ['都江堰市', '都江堰'],
            '金堂': ['金堂县', '金堂'],
            '金牛': ['金牛区', '金牛'],
            '锦江': ['锦江区', '锦江'],
            '青白江': ['青白江区', '青白江'],
            '青羊': ['青羊区', '青羊'],
            '龙泉驿': ['龙泉驿区', '龙泉驿', '龙泉']
        }
        
        # 区域到最近空气质量监测城市的映射
        self.region_to_city_mapping = {
            '简阳': '成都', '成华': '成都', '双流': '成都', '大邑': '成都',
            '崇州': '成都', '彭州': '成都', '新津': '成都', '新都': '成都',
            '武侯': '成都', '温江': '成都', '蒲江': '成都', '邛崃': '成都',
            '郫都': '成都', '都江堰': '成都', '金堂': '成都', '金牛': '成都',
            '锦江': '成都', '青白江': '成都', '青羊': '成都', '龙泉驿': '成都',
            '甘孜': '甘孜藏族自治州'
        }
        
        logger.info("最终版大宽表创建器初始化完成")
    
    def extract_region_from_address(self, address: str) -> Optional[str]:
        """从地址中提取区域信息"""
        if pd.isna(address):
            return None
        
        address = str(address)
        for region, address_patterns in self.address_region_mapping.items():
            for pattern in address_patterns:
                if pattern in address:
                    return region
        return None
    
    def load_load_data(self, sample_size: Optional[int] = None) -> pd.DataFrame:
        """加载负荷数据"""
        if sample_size:
            logger.info(f"加载负荷数据样本 ({sample_size} 行)...")
            self.load_data = pd.read_csv('complete_load_analysis_data.csv', nrows=sample_size)
        else:
            logger.info("加载完整负荷数据集...")
            chunk_size = 50000
            chunks = []
            for i, chunk in enumerate(pd.read_csv('complete_load_analysis_data.csv', chunksize=chunk_size)):
                chunks.append(chunk)
                if (i + 1) % 5 == 0:
                    logger.info(f"已加载 {(i + 1) * chunk_size} 行数据...")
            self.load_data = pd.concat(chunks, ignore_index=True)
        
        # 时间处理
        self.load_data['Timestamp'] = pd.to_datetime(self.load_data['Timestamp'])
        
        # 添加时间特征
        self.load_data['Year'] = self.load_data['Timestamp'].dt.year
        self.load_data['Month'] = self.load_data['Timestamp'].dt.month
        self.load_data['Day'] = self.load_data['Timestamp'].dt.day
        self.load_data['Hour'] = self.load_data['Timestamp'].dt.hour
        self.load_data['DayOfWeek'] = self.load_data['Timestamp'].dt.dayofweek
        self.load_data['IsWeekend'] = (self.load_data['DayOfWeek'] >= 5).astype(int)
        
        # 季节特征
        self.load_data['Season'] = self.load_data['Month'].map({
            12: 'Winter', 1: 'Winter', 2: 'Winter',
            3: 'Spring', 4: 'Spring', 5: 'Spring',
            6: 'Summer', 7: 'Summer', 8: 'Summer',
            9: 'Autumn', 10: 'Autumn', 11: 'Autumn'
        })
        
        # 从地址提取区域信息
        if 'ConsumerAddress' in self.load_data.columns:
            self.load_data['Region'] = self.load_data['ConsumerAddress'].apply(
                self.extract_region_from_address
            )
            region_counts = self.load_data['Region'].value_counts()
            logger.info(f"区域分布: {dict(region_counts.head(10))}")
        
        logger.info(f"负荷数据加载完成: {self.load_data.shape}")
        return self.load_data
    
    def load_weather_data(self) -> Dict[str, pd.DataFrame]:
        """加载气象数据（简化版）"""
        logger.info("加载气象数据...")
        
        weather_files = glob.glob('天气数据/*.xls') + glob.glob('天气数据/*.xlsx')
        
        for file_path in weather_files:
            file_name = Path(file_path).stem
            
            # 跳过空气质量数据文件
            if '城市24小时' in file_name:
                continue
            
            # 从文件名解析区域和年份
            region = None
            year = None
            
            for known_region in self.address_region_mapping.keys():
                if known_region in file_name:
                    region = known_region
                    break
            
            if '23' in file_name:
                year = 2023
            elif '24' in file_name:
                year = 2024
            elif '25' in file_name:
                year = 2025
            
            if region and year:
                try:
                    df = pd.read_excel(file_path)
                    
                    # 处理时间列
                    time_col = None
                    for col in df.columns:
                        if any(keyword in str(col) for keyword in ['北京时', 'UTC+8']):
                            time_col = col
                            break
                    
                    if time_col:
                        df['Timestamp'] = pd.to_datetime(df[time_col])
                        df = df.sort_values('Timestamp').reset_index(drop=True)
                        
                        # 只保留主要的数值列
                        key_weather_cols = ['海平面气压(hPa)', '气温2m(℃)', '相对湿度(%)', 
                                          '降水量(mm)', '纬向风速(U,m/s)', '经向风速(V,m/s)']
                        
                        # 清理数值列
                        for col in key_weather_cols:
                            if col in df.columns:
                                # 确保是数值类型
                                df[col] = pd.to_numeric(df[col], errors='coerce')
                        
                        # 添加元数据
                        df['region'] = region
                        df['year'] = year
                        
                        key = f"{region}_{year}"
                        self.weather_data[key] = df
                        logger.info(f"加载气象数据 {key}: {df.shape}")
                    else:
                        logger.warning(f"气象文件 {file_name} 未找到时间列")
                
                except Exception as e:
                    logger.warning(f"加载气象文件失败 {file_name}: {e}")
        
        logger.info(f"气象数据加载完成: {len(self.weather_data)} 个数据集")
        return self.weather_data
    
    def load_air_quality_data(self) -> Dict[str, pd.DataFrame]:
        """加载空气质量数据"""
        logger.info("加载空气质量数据...")
        
        air_quality_files = glob.glob('天气数据/城市24小时*.xlsx')
        
        for file_path in air_quality_files:
            file_name = Path(file_path).stem
            
            try:
                df = pd.read_excel(file_path)
                
                if '日期' in df.columns and '小时' in df.columns:
                    df['Timestamp'] = pd.to_datetime(df['日期']) + pd.to_timedelta(df['小时'], unit='h')
                    df = df.sort_values('Timestamp').reset_index(drop=True)
                    
                    if '2023' in file_name:
                        year = 2023
                    elif '24-25' in file_name:
                        year = 2024
                    else:
                        year = df['Timestamp'].dt.year.iloc[0]
                    
                    key = f"air_quality_{year}"
                    self.air_quality_data[key] = df
                    logger.info(f"加载空气质量数据 {key}: {df.shape}")
                
            except Exception as e:
                logger.warning(f"加载空气质量文件失败 {file_path}: {e}")
        
        logger.info(f"空气质量数据加载完成: {len(self.air_quality_data)} 个数据集")
        return self.air_quality_data

    def merge_single_weather_feature(self, wide_table: pd.DataFrame, weather_df: pd.DataFrame,
                                   region: str, year: int, feature_col: str, new_col_name: str) -> pd.DataFrame:
        """合并单个气象特征（简化版）"""
        try:
            # 筛选匹配的负荷数据
            mask = (wide_table['Year'] == year) & (wide_table['Region'] == region)
            matching_records = mask.sum()

            if matching_records == 0:
                return wide_table

            # 准备气象数据
            weather_for_merge = weather_df[['Timestamp', feature_col]].copy()
            weather_for_merge = weather_for_merge.rename(columns={feature_col: new_col_name})
            weather_for_merge = weather_for_merge.dropna()

            if len(weather_for_merge) == 0:
                return wide_table

            # 获取负荷数据子集
            load_subset = wide_table[mask].copy().sort_values('Timestamp')
            weather_for_merge = weather_for_merge.sort_values('Timestamp')

            # 合并
            merged_subset = pd.merge_asof(
                load_subset,
                weather_for_merge,
                on='Timestamp',
                direction='nearest'
            )

            # 更新原始数据
            if new_col_name in merged_subset.columns:
                wide_table.loc[mask, new_col_name] = merged_subset[new_col_name].values
                logger.info(f"成功合并 {new_col_name}，覆盖 {matching_records} 条记录")

            return wide_table

        except Exception as e:
            logger.warning(f"合并 {new_col_name} 失败: {e}")
            return wide_table

    def create_wide_table(self) -> pd.DataFrame:
        """创建最终版综合大宽表"""
        logger.info("开始创建最终版综合大宽表...")

        if self.load_data is None:
            raise ValueError("请先加载负荷数据")

        wide_table = self.load_data.copy()
        logger.info(f"起始数据形状: {wide_table.shape}")

        # 1. 合并气象数据（逐个特征合并）
        weather_merged_count = 0
        weather_features_added = []

        for key, weather_df in self.weather_data.items():
            region, year = key.split('_')
            year = int(year)

            logger.info(f"处理气象数据 {key}...")

            # 定义要合并的气象特征
            weather_feature_mapping = {
                '海平面气压(hPa)': f'Pressure_{region}',
                '气温2m(℃)': f'Temperature_{region}',
                '相对湿度(%)': f'Humidity_{region}',
                '降水量(mm)': f'Precipitation_{region}',
                '纬向风速(U,m/s)': f'WindSpeed_U_{region}',
                '经向风速(V,m/s)': f'WindSpeed_V_{region}'
            }

            # 逐个合并特征
            for original_col, new_col in weather_feature_mapping.items():
                if original_col in weather_df.columns:
                    wide_table = self.merge_single_weather_feature(
                        wide_table, weather_df, region, year, original_col, new_col
                    )
                    if new_col not in weather_features_added:
                        weather_features_added.append(new_col)

            weather_merged_count += 1

        # 2. 合并空气质量数据（保持原有逻辑）
        air_quality_merged_count = 0
        aq_features_added = []

        for key, aq_df in self.air_quality_data.items():
            year = int(key.split('_')[-1])

            year_mask = (wide_table['Year'] == year)
            year_records = year_mask.sum()

            if year_records == 0:
                continue

            logger.info(f"处理空气质量数据 {key}，{year_records} 条年份匹配记录")

            # 按区域分组处理
            for region, city in self.region_to_city_mapping.items():
                if '城市' not in aq_df.columns or city not in aq_df['城市'].values:
                    continue

                region_mask = year_mask & (wide_table['Region'] == region)
                region_records = region_mask.sum()

                if region_records == 0:
                    continue

                city_aq_data = aq_df[aq_df['城市'] == city].copy()

                if len(city_aq_data) == 0:
                    continue

                aq_features = ['AQI', 'PM2_5', 'PM10', 'SO2', 'NO2', 'O3', 'CO']
                available_features = [col for col in aq_features if col in city_aq_data.columns]

                if available_features:
                    rename_dict = {col: f'{col}_{region}' for col in available_features}
                    aq_for_merge = city_aq_data[['Timestamp'] + available_features].copy()
                    aq_for_merge = aq_for_merge.rename(columns=rename_dict)
                    aq_for_merge = aq_for_merge.dropna(subset=['Timestamp'])

                    try:
                        load_subset = wide_table[region_mask].copy().sort_values('Timestamp')
                        aq_for_merge = aq_for_merge.sort_values('Timestamp')

                        merged_subset = pd.merge_asof(
                            load_subset,
                            aq_for_merge,
                            on='Timestamp',
                            direction='nearest'
                        )

                        for new_col in rename_dict.values():
                            if new_col in merged_subset.columns:
                                wide_table.loc[region_mask, new_col] = merged_subset[new_col].values
                                if new_col not in aq_features_added:
                                    aq_features_added.append(new_col)

                        logger.info(f"空气质量数据 {region}-{city} 合并完成")

                    except Exception as e:
                        logger.warning(f"空气质量数据 {region}-{city} 合并失败: {e}")

            air_quality_merged_count += 1

        self.wide_table = wide_table
        logger.info(f"最终版综合大宽表创建完成，形状: {wide_table.shape}")
        logger.info(f"合并了 {weather_merged_count} 个气象数据集，{air_quality_merged_count} 个空气质量数据集")
        logger.info(f"添加了 {len(weather_features_added)} 个气象特征，{len(aq_features_added)} 个空气质量特征")

        return wide_table

    def save_wide_table(self, filename: str = None) -> str:
        """保存最终版大宽表"""
        if self.wide_table is None:
            raise ValueError("请先创建大宽表")

        if filename is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"final_wide_table_{timestamp}.csv"

        filepath = os.path.join('data/processed', filename)

        self.wide_table.to_csv(filepath, index=False, encoding='utf-8')
        logger.info(f"最终版大宽表已保存至: {filepath}")

        # 生成详细摘要
        summary_file = filepath.replace('.csv', '_summary.txt')
        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write("最终版大宽表数据摘要\n")
            f.write("=" * 60 + "\n")
            f.write(f"创建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"数据形状: {self.wide_table.shape}\n")
            f.write(f"时间范围: {self.wide_table['Timestamp'].min()} 到 {self.wide_table['Timestamp'].max()}\n\n")

            # 区域分布
            f.write("区域分布:\n")
            f.write("-" * 30 + "\n")
            if 'Region' in self.wide_table.columns:
                region_counts = self.wide_table['Region'].value_counts()
                for region, count in region_counts.items():
                    pct = count / len(self.wide_table) * 100
                    f.write(f"  {region}: {count} ({pct:.1f}%)\n")

            # 特征统计
            f.write("\n特征统计:\n")
            f.write("-" * 30 + "\n")

            weather_cols = [col for col in self.wide_table.columns if any(keyword in col for keyword in
                           ['Temperature', 'Pressure', 'Humidity', 'Precipitation', 'WindSpeed'])]
            f.write(f"气象特征数: {len(weather_cols)}\n")

            aq_cols = [col for col in self.wide_table.columns if any(keyword in col for keyword in
                      ['AQI', 'PM2_5', 'PM10', 'SO2', 'NO2', 'O3', 'CO'])]
            f.write(f"空气质量特征数: {len(aq_cols)}\n")

            # 数据覆盖率
            f.write("\n数据覆盖率:\n")
            f.write("-" * 30 + "\n")
            all_new_cols = weather_cols + aq_cols
            for col in all_new_cols:
                if col in self.wide_table.columns:
                    non_null = self.wide_table[col].notna().sum()
                    pct = (non_null / len(self.wide_table)) * 100
                    f.write(f"  {col}: {pct:.1f}%\n")

        logger.info(f"数据摘要已保存至: {summary_file}")
        return filepath

def main():
    """主函数"""
    print("空调负荷柔性调控能力分析系统 - 最终版大宽表生成")
    print("=" * 70)

    try:
        creator = FinalWideTableCreator()

        # 1. 加载负荷数据
        print("1. 加载负荷数据...")
        sample_size = input("请输入样本大小（直接回车加载全部数据）: ").strip()
        if sample_size and sample_size.isdigit():
            creator.load_load_data(sample_size=int(sample_size))
        else:
            creator.load_load_data(sample_size=None)

        # 2. 加载气象数据
        print("2. 加载气象数据...")
        creator.load_weather_data()

        # 3. 加载空气质量数据
        print("3. 加载空气质量数据...")
        creator.load_air_quality_data()

        # 4. 创建最终版大宽表
        print("4. 创建最终版大宽表...")
        wide_table = creator.create_wide_table()
        print(f"   最终版大宽表创建完成: {wide_table.shape}")

        # 5. 保存大宽表
        print("5. 保存最终版大宽表...")
        filepath = creator.save_wide_table()
        print(f"   最终版大宽表已保存至: {filepath}")

        # 6. 显示摘要信息
        print("\n6. 最终版大宽表摘要:")
        print(f"   数据形状: {wide_table.shape}")
        print(f"   时间范围: {wide_table['Timestamp'].min()} 到 {wide_table['Timestamp'].max()}")

        if 'Region' in wide_table.columns:
            region_counts = wide_table['Region'].value_counts()
            print(f"   覆盖区域: {len(region_counts)} 个")
            print("   主要区域分布:")
            for region, count in region_counts.head(5).items():
                pct = count / len(wide_table) * 100
                print(f"     {region}: {count} ({pct:.1f}%)")

        weather_cols = [col for col in wide_table.columns if any(keyword in col for keyword in
                       ['Temperature', 'Pressure', 'Humidity', 'Precipitation', 'WindSpeed'])]
        aq_cols = [col for col in wide_table.columns if any(keyword in col for keyword in
                  ['AQI', 'PM2_5', 'PM10', 'SO2', 'NO2', 'O3', 'CO'])]

        print(f"   气象特征数: {len(weather_cols)}")
        print(f"   空气质量特征数: {len(aq_cols)}")

        print("\n" + "=" * 70)
        print("最终版大宽表生成完成！")
        print("最终修复要点:")
        print("✓ 逐个特征合并，避免多维数组问题")
        print("✓ 简化气象数据处理逻辑")
        print("✓ 保持空气质量数据合并的成功逻辑")
        print("✓ 增强错误处理和日志记录")

    except Exception as e:
        logger.error(f"最终版大宽表生成失败: {e}")
        print(f"错误: {e}")
        return 1

    return 0

if __name__ == "__main__":
    exit(main())
