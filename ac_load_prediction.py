#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
空调负荷柔性调控能力分析系统 - 空调负荷预测与柔性调控评估

基于训练好的模型进行空调负荷识别和柔性调控能力评估

作者: AI Assistant
创建时间: 2025-07-13
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
import logging
import os
import json
import joblib
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple

warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ACLoadPredictor:
    """空调负荷预测器"""
    
    def __init__(self, model_dir: str = 'models'):
        """
        初始化预测器
        
        Args:
            model_dir: 模型文件目录
        """
        self.model_dir = Path(model_dir)
        self.model = None
        self.scaler = None
        self.features = None
        self.model_info = None
        
        # 创建输出目录
        Path('results').mkdir(exist_ok=True)
        Path('figures').mkdir(exist_ok=True)
        
        logger.info("空调负荷预测器初始化完成")
    
    def load_model(self) -> bool:
        """加载训练好的模型"""
        try:
            # 加载模型文件
            model_file = self.model_dir / 'ac_load_model.pkl'
            scaler_file = self.model_dir / 'ac_load_scaler.pkl'
            features_file = self.model_dir / 'ac_load_features.json'
            
            if not all([model_file.exists(), scaler_file.exists(), features_file.exists()]):
                logger.error("模型文件不完整")
                return False
            
            # 加载模型和预处理器
            self.model = joblib.load(model_file)
            self.scaler = joblib.load(scaler_file)
            
            # 加载特征信息
            with open(features_file, 'r', encoding='utf-8') as f:
                self.model_info = json.load(f)
                self.features = self.model_info['features']
            
            logger.info(f"模型加载成功: {self.model_info['model_type']}")
            logger.info(f"模型性能 - R²: {self.model_info['performance']['test_r2']:.4f}")
            logger.info(f"特征数量: {len(self.features)}")
            
            return True
            
        except Exception as e:
            logger.error(f"模型加载失败: {e}")
            return False
    
    def predict_ac_load(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        预测空调负荷
        
        Args:
            data: 输入数据
            
        Returns:
            包含预测结果的DataFrame
        """
        if self.model is None:
            raise ValueError("请先加载模型")
        
        logger.info("开始预测空调负荷...")
        
        # 准备特征数据
        missing_features = [f for f in self.features if f not in data.columns]
        if missing_features:
            logger.warning(f"缺少特征: {missing_features}")
            # 用0填充缺少的特征
            for feature in missing_features:
                data[feature] = 0
        
        # 提取特征
        X = data[self.features].copy()
        
        # 处理缺失值
        X = X.fillna(0)
        
        # 标准化
        X_scaled = self.scaler.transform(X)
        
        # 预测
        ac_load_pred = self.model.predict(X_scaled)
        
        # 确保预测值非负
        ac_load_pred = np.maximum(ac_load_pred, 0)
        
        # 添加预测结果到原数据
        result = data.copy()
        result['ac_load_predicted'] = ac_load_pred
        
        # 计算空调负荷比例
        if 'HourlyAvgLoad' in result.columns:
            result['ac_load_ratio'] = result['ac_load_predicted'] / (result['HourlyAvgLoad'] + 1e-10)
            result['ac_load_ratio'] = result['ac_load_ratio'].clip(0, 1)
        
        logger.info(f"空调负荷预测完成，平均空调负荷: {ac_load_pred.mean():.2f}")
        
        return result
    
    def evaluate_flexibility(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        评估柔性调控能力
        
        Args:
            data: 包含空调负荷预测的数据
            
        Returns:
            包含柔性调控评估的DataFrame
        """
        logger.info("开始评估柔性调控能力...")
        
        result = data.copy()
        
        if 'ac_load_predicted' not in result.columns:
            logger.error("请先进行空调负荷预测")
            return result
        
        # 1. 基于时间的柔性调控能力
        # 工作日vs周末的调控潜力不同
        weekday_flexibility = 0.25  # 工作日25%可调控
        weekend_flexibility = 0.35  # 周末35%可调控
        
        result['base_flexibility'] = np.where(
            result.get('IsWeekend', 0) == 1,
            weekend_flexibility,
            weekday_flexibility
        )
        
        # 2. 基于时段的柔性调控能力
        # 不同时段的调控潜力不同
        hour_flexibility_map = {
            # 深夜时段 (0-6): 高调控潜力
            **{h: 0.4 for h in range(0, 7)},
            # 早晨时段 (7-9): 中等调控潜力
            **{h: 0.2 for h in range(7, 10)},
            # 上午时段 (10-12): 低调控潜力
            **{h: 0.15 for h in range(10, 13)},
            # 下午时段 (13-17): 中等调控潜力
            **{h: 0.25 for h in range(13, 18)},
            # 晚上时段 (18-22): 低调控潜力
            **{h: 0.2 for h in range(18, 23)},
            # 夜间时段 (23): 中等调控潜力
            23: 0.3
        }
        
        if 'Hour' in result.columns:
            result['hour_flexibility'] = result['Hour'].map(hour_flexibility_map).fillna(0.25)
        else:
            result['hour_flexibility'] = 0.25
        
        # 3. 基于季节的柔性调控能力
        # 夏季和冬季空调使用较多，调控潜力较大
        season_flexibility_map = {
            6: 0.35,   # 6月
            7: 0.4,    # 7月 - 最高
            8: 0.4,    # 8月 - 最高
            9: 0.3,    # 9月
            12: 0.3,   # 12月
            1: 0.35,   # 1月
            2: 0.3,    # 2月
        }
        
        if 'Month' in result.columns:
            result['season_flexibility'] = result['Month'].map(season_flexibility_map).fillna(0.2)
        else:
            result['season_flexibility'] = 0.2
        
        # 4. 基于空气质量的调控能力调整
        # 空气质量差时，调控能力可能受限
        if 'AQI_成都' in result.columns:
            # AQI越高，调控能力越受限
            aqi_factor = np.where(
                result['AQI_成都'] > 150,  # 重度污染
                0.8,  # 调控能力降低20%
                np.where(
                    result['AQI_成都'] > 100,  # 轻度污染
                    0.9,  # 调控能力降低10%
                    1.0   # 无影响
                )
            )
            result['aqi_factor'] = aqi_factor
        else:
            result['aqi_factor'] = 1.0
        
        # 5. 综合柔性调控能力计算
        # 综合考虑各种因素
        result['flexibility_factor'] = (
            result['base_flexibility'] * 0.3 +
            result['hour_flexibility'] * 0.4 +
            result['season_flexibility'] * 0.3
        ) * result['aqi_factor']
        
        # 确保柔性调控因子在合理范围内
        result['flexibility_factor'] = result['flexibility_factor'].clip(0.05, 0.5)
        
        # 6. 计算可调控负荷
        result['controllable_load'] = result['ac_load_predicted'] * result['flexibility_factor']
        
        # 7. 分类调控能力等级
        result['flexibility_level'] = pd.cut(
            result['flexibility_factor'],
            bins=[0, 0.15, 0.25, 0.35, 1.0],
            labels=['低', '中', '高', '很高'],
            include_lowest=True
        )
        
        logger.info(f"柔性调控能力评估完成")
        logger.info(f"平均可调控负荷: {result['controllable_load'].mean():.2f}")
        logger.info(f"平均调控因子: {result['flexibility_factor'].mean():.3f}")
        
        return result
    
    def generate_analysis_report(self, data: pd.DataFrame) -> str:
        """生成分析报告"""
        logger.info("生成分析报告...")
        
        report = []
        report.append("=" * 80)
        report.append("空调负荷柔性调控能力分析报告")
        report.append("=" * 80)
        report.append(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"数据记录数: {len(data)}")
        
        if 'Timestamp' in data.columns:
            report.append(f"时间范围: {data['Timestamp'].min()} 到 {data['Timestamp'].max()}")
        
        report.append("")
        
        # 1. 空调负荷识别结果
        if 'ac_load_predicted' in data.columns:
            report.append("1. 空调负荷识别结果")
            report.append("-" * 40)
            report.append(f"平均空调负荷: {data['ac_load_predicted'].mean():.2f} kW")
            report.append(f"最大空调负荷: {data['ac_load_predicted'].max():.2f} kW")
            report.append(f"空调负荷标准差: {data['ac_load_predicted'].std():.2f} kW")
            
            if 'ac_load_ratio' in data.columns:
                report.append(f"平均空调负荷比例: {data['ac_load_ratio'].mean():.1%}")
                report.append(f"最大空调负荷比例: {data['ac_load_ratio'].max():.1%}")
            
            report.append("")
        
        # 2. 柔性调控能力评估
        if 'controllable_load' in data.columns:
            report.append("2. 柔性调控能力评估")
            report.append("-" * 40)
            report.append(f"平均可调控负荷: {data['controllable_load'].mean():.2f} kW")
            report.append(f"最大可调控负荷: {data['controllable_load'].max():.2f} kW")
            report.append(f"总可调控潜力: {data['controllable_load'].sum():.2f} kWh")
            report.append(f"平均调控因子: {data['flexibility_factor'].mean():.1%}")
            
            # 调控能力等级分布
            if 'flexibility_level' in data.columns:
                level_counts = data['flexibility_level'].value_counts()
                report.append("\n调控能力等级分布:")
                for level, count in level_counts.items():
                    pct = count / len(data) * 100
                    report.append(f"  {level}: {count} ({pct:.1f}%)")
            
            report.append("")
        
        # 3. 时间特征分析
        if 'Hour' in data.columns:
            report.append("3. 时间特征分析")
            report.append("-" * 40)
            
            # 按小时统计
            hourly_stats = data.groupby('Hour').agg({
                'ac_load_predicted': 'mean',
                'controllable_load': 'mean'
            }).round(2)
            
            peak_hour = hourly_stats['ac_load_predicted'].idxmax()
            peak_load = hourly_stats['ac_load_predicted'].max()
            
            report.append(f"空调负荷峰值时段: {peak_hour}:00")
            report.append(f"峰值空调负荷: {peak_load:.2f} kW")
            
            # 最佳调控时段
            best_control_hour = hourly_stats['controllable_load'].idxmax()
            best_control_load = hourly_stats['controllable_load'].max()
            
            report.append(f"最佳调控时段: {best_control_hour}:00")
            report.append(f"最大可调控负荷: {best_control_load:.2f} kW")
            
            report.append("")
        
        # 4. 建议
        report.append("4. 调控策略建议")
        report.append("-" * 40)
        
        if 'controllable_load' in data.columns:
            total_potential = data['controllable_load'].sum()
            avg_ratio = data['flexibility_factor'].mean()
            
            if avg_ratio > 0.3:
                report.append("✓ 柔性调控潜力较大，建议积极开展需求响应")
            elif avg_ratio > 0.2:
                report.append("✓ 柔性调控潜力中等，可适度开展需求响应")
            else:
                report.append("• 柔性调控潜力较小，需谨慎制定调控策略")
            
            report.append(f"• 总调控潜力约 {total_potential:.0f} kWh")
            report.append("• 建议在夏季高温和冬季低温时段重点实施调控")
            report.append("• 建议在深夜和周末时段优先实施调控")
            
            if 'AQI_成都' in data.columns:
                high_aqi_days = (data['AQI_成都'] > 150).sum()
                if high_aqi_days > 0:
                    report.append(f"• 注意空气质量影响，重污染天数约 {high_aqi_days} 个时段")
        
        report.append("")
        report.append("5. 技术说明")
        report.append("-" * 40)
        report.append(f"• 使用模型: {self.model_info['model_type']}")
        report.append(f"• 模型精度: R² = {self.model_info['performance']['test_r2']:.4f}")
        report.append("• 调控能力基于时间、季节、空气质量等因素综合评估")
        report.append("• 建议结合实际运行情况进一步优化调控策略")
        
        report_text = "\n".join(report)
        
        # 保存报告
        report_file = 'results/ac_load_analysis_report.txt'
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_text)
        
        logger.info(f"分析报告已保存至: {report_file}")
        return report_text

    def create_visualizations(self, data: pd.DataFrame) -> None:
        """创建可视化图表"""
        logger.info("创建可视化图表...")

        # 设置图表样式
        plt.style.use('default')

        # 1. 空调负荷时间序列图
        if 'Timestamp' in data.columns and 'ac_load_predicted' in data.columns:
            fig, axes = plt.subplots(2, 2, figsize=(16, 12))
            fig.suptitle('空调负荷柔性调控能力分析', fontsize=16, fontweight='bold')

            # 时间序列图
            ax1 = axes[0, 0]
            sample_data = data.sample(min(1000, len(data))).sort_values('Timestamp')
            ax1.plot(sample_data['Timestamp'], sample_data['ac_load_predicted'],
                    alpha=0.7, linewidth=1, label='空调负荷')
            if 'controllable_load' in sample_data.columns:
                ax1.plot(sample_data['Timestamp'], sample_data['controllable_load'],
                        alpha=0.7, linewidth=1, label='可调控负荷')
            ax1.set_title('空调负荷时间序列')
            ax1.set_xlabel('时间')
            ax1.set_ylabel('负荷 (kW)')
            ax1.legend()
            ax1.grid(True, alpha=0.3)

            # 小时分布图
            ax2 = axes[0, 1]
            if 'Hour' in data.columns:
                hourly_stats = data.groupby('Hour').agg({
                    'ac_load_predicted': 'mean',
                    'controllable_load': 'mean'
                })

                ax2.bar(hourly_stats.index, hourly_stats['ac_load_predicted'],
                       alpha=0.7, label='平均空调负荷')
                if 'controllable_load' in hourly_stats.columns:
                    ax2.bar(hourly_stats.index, hourly_stats['controllable_load'],
                           alpha=0.7, label='平均可调控负荷')

                ax2.set_title('24小时负荷分布')
                ax2.set_xlabel('小时')
                ax2.set_ylabel('平均负荷 (kW)')
                ax2.legend()
                ax2.grid(True, alpha=0.3)

            # 柔性调控能力分布
            ax3 = axes[1, 0]
            if 'flexibility_factor' in data.columns:
                ax3.hist(data['flexibility_factor'], bins=30, alpha=0.7, edgecolor='black')
                ax3.axvline(data['flexibility_factor'].mean(), color='red', linestyle='--',
                           label=f'平均值: {data["flexibility_factor"].mean():.3f}')
                ax3.set_title('柔性调控因子分布')
                ax3.set_xlabel('调控因子')
                ax3.set_ylabel('频次')
                ax3.legend()
                ax3.grid(True, alpha=0.3)

            # 调控能力等级饼图
            ax4 = axes[1, 1]
            if 'flexibility_level' in data.columns:
                level_counts = data['flexibility_level'].value_counts()
                colors = ['#ff9999', '#66b3ff', '#99ff99', '#ffcc99']
                ax4.pie(level_counts.values, labels=level_counts.index, autopct='%1.1f%%',
                       colors=colors[:len(level_counts)])
                ax4.set_title('调控能力等级分布')

            plt.tight_layout()

            # 保存图表
            plot_file = 'figures/ac_load_analysis.png'
            plt.savefig(plot_file, dpi=300, bbox_inches='tight')
            logger.info(f"可视化图表已保存至: {plot_file}")
            plt.close()

        # 2. 相关性分析热图
        if len(data.columns) > 10:
            fig, ax = plt.subplots(figsize=(12, 10))

            # 选择数值列进行相关性分析
            numeric_cols = data.select_dtypes(include=[np.number]).columns
            key_cols = [col for col in numeric_cols if any(keyword in col.lower()
                       for keyword in ['load', 'aqi', 'pm', 'hour', 'month', 'flexibility'])]

            if len(key_cols) > 3:
                corr_data = data[key_cols[:15]].corr()  # 最多15个特征

                mask = np.triu(np.ones_like(corr_data, dtype=bool))
                sns.heatmap(corr_data, mask=mask, annot=True, cmap='coolwarm', center=0,
                           square=True, linewidths=0.5, cbar_kws={"shrink": 0.8}, ax=ax)

                ax.set_title('特征相关性分析', fontsize=14, fontweight='bold')

                plt.tight_layout()

                # 保存相关性图
                corr_file = 'figures/correlation_analysis.png'
                plt.savefig(corr_file, dpi=300, bbox_inches='tight')
                logger.info(f"相关性分析图已保存至: {corr_file}")
                plt.close()

def main():
    """主函数"""
    print("空调负荷柔性调控能力分析系统 - 预测与评估")
    print("=" * 60)

    try:
        # 1. 初始化预测器
        predictor = ACLoadPredictor()

        # 2. 加载模型
        print("1. 加载训练好的模型...")
        if not predictor.load_model():
            print("错误: 模型加载失败")
            return 1

        # 3. 加载测试数据
        print("2. 加载测试数据...")

        # 查找最新的综合大宽表文件
        processed_dir = Path('data/processed')
        wide_table_files = list(processed_dir.glob('comprehensive_wide_table_*.csv'))

        if not wide_table_files:
            print("错误: 未找到数据文件")
            return 1

        latest_file = max(wide_table_files, key=lambda x: x.stat().st_mtime)
        print(f"   使用数据文件: {latest_file}")

        # 加载数据（使用部分数据进行演示）
        data = pd.read_csv(latest_file, nrows=5000)  # 加载5000行数据
        data['Timestamp'] = pd.to_datetime(data['Timestamp'])
        print(f"   数据形状: {data.shape}")

        # 4. 空调负荷预测
        print("3. 进行空调负荷预测...")
        data_with_prediction = predictor.predict_ac_load(data)

        # 5. 柔性调控能力评估
        print("4. 评估柔性调控能力...")
        data_with_flexibility = predictor.evaluate_flexibility(data_with_prediction)

        # 6. 生成分析报告
        print("5. 生成分析报告...")
        report = predictor.generate_analysis_report(data_with_flexibility)

        # 7. 创建可视化图表
        print("6. 创建可视化图表...")
        predictor.create_visualizations(data_with_flexibility)

        # 8. 显示关键结果
        print("\n" + "=" * 60)
        print("分析结果摘要:")
        print(f"• 平均空调负荷: {data_with_flexibility['ac_load_predicted'].mean():.2f} kW")
        print(f"• 平均可调控负荷: {data_with_flexibility['controllable_load'].mean():.2f} kW")
        print(f"• 平均调控因子: {data_with_flexibility['flexibility_factor'].mean():.1%}")

        if 'flexibility_level' in data_with_flexibility.columns:
            level_counts = data_with_flexibility['flexibility_level'].value_counts()
            print("• 调控能力分布:")
            for level, count in level_counts.items():
                pct = count / len(data_with_flexibility) * 100
                print(f"  - {level}: {pct:.1f}%")

        print(f"\n详细结果请查看:")
        print("• 分析报告: results/ac_load_analysis_report.txt")
        print("• 可视化图表: figures/ac_load_analysis.png")
        print("• 相关性分析: figures/correlation_analysis.png")

        print("\n" + "=" * 60)
        print("空调负荷柔性调控能力分析完成！")
        print("该分析结果可用于:")
        print("✓ 制定需求响应策略")
        print("✓ 优化电网调度计划")
        print("✓ 评估负荷侧资源潜力")
        print("✓ 支持电力市场交易决策")

    except Exception as e:
        logger.error(f"分析失败: {e}")
        print(f"错误: {e}")
        return 1

    return 0

if __name__ == "__main__":
    exit(main())
