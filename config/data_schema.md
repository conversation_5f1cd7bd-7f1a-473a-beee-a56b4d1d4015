# 空调负荷柔性调控能力分析系统 - 数据规范文档

## 1. 数据文件命名规范

### 1.1 原始数据文件
```
原始数据/
├── load/
│   └── complete_load_analysis_data.csv          # 负荷数据
├── weather/
│   ├── regional/                                # 区域天气数据
│   │   ├── 双流_2023.xlsx
│   │   ├── 双流_2024.xlsx
│   │   ├── 双流_2025.xlsx
│   │   └── ...
│   └── city_24h/                                # 城市24小时数据
│       ├── city_24h_2023.xlsx
│       └── city_24h_2024_2025.xlsx
└── metadata/
    └── data_dictionary.xlsx                     # 数据字典
```

### 1.2 处理后数据文件
```
data/
├── raw/                                         # 原始数据（标准化后）
│   ├── load_data.parquet                        # 负荷数据
│   ├── weather_regional.parquet                 # 区域天气数据
│   └── weather_city_24h.parquet                 # 城市24小时数据
├── processed/                                   # 处理后的数据
│   ├── load_cleaned.parquet                     # 清洗后的负荷数据
│   ├── weather_cleaned.parquet                  # 清洗后的天气数据
│   └── merged_data.parquet                      # 合并后的大宽表
└── interim/                                     # 中间数据
    ├── load_features.parquet                    # 负荷特征
    ├── weather_features.parquet                 # 天气特征
    └── temporal_features.parquet                # 时间特征
```

## 2. 数据格式规范

### 2.1 负荷数据格式
```python
load_data_schema = {
    # 空间维度
    'substation_id': 'int64',           # 变电站ID
    'substation_name': 'string',        # 变电站名称
    'circuit_id': 'int64',              # 线路ID
    'circuit_name': 'string',           # 线路名称
    'feeder_id': 'int64',               # 馈线ID
    'feeder_name': 'string',            # 馈线名称
    'consumer_id': 'string',            # 用户ID
    'consumer_name': 'string',          # 用户名称
    'consumer_type': 'string',          # 用户类型
    'consumer_address': 'string',       # 用户地址
    
    # 时间维度
    'timestamp': 'datetime64[ns]',      # 时间戳（统一为北京时间）
    
    # 负荷指标（小时级）
    'hourly_avg_load': 'float64',       # 小时平均负荷
    'hourly_max_load': 'float64',       # 小时最大负荷
    'hourly_min_load': 'float64',       # 小时最小负荷
    'hourly_std_dev': 'float64',        # 小时标准差
    'hourly_active_energy': 'float64',  # 小时有功电量
    'hourly_reactive_energy': 'float64', # 小时无功电量
    'hourly_power_factor': 'float64',   # 小时功率因数
    'hourly_load_factor': 'float64',    # 小时负荷率
    
    # 负荷指标（日级）
    'daily_max_load': 'float64',        # 日最大负荷
    'daily_max_load_time': 'datetime64[ns]', # 日最大负荷时间
    'daily_min_load': 'float64',        # 日最小负荷
    'daily_min_load_time': 'datetime64[ns]', # 日最小负荷时间
    'daily_avg_load': 'float64',        # 日平均负荷
    'daily_total_energy': 'float64',    # 日总电量
    'daily_peak_valley_diff': 'float64', # 日峰谷差
    'daily_peak_valley_ratio': 'float64', # 日峰谷比
    'daily_load_factor': 'float64',     # 日负荷率
    'daily_peak_load': 'float64',       # 日峰荷
    'daily_normal_load': 'float64',     # 日平荷
    'daily_valley_load': 'float64',     # 日谷荷
    
    # 负荷指标（周级）
    'weekly_max_load': 'float64',       # 周最大负荷
    'weekly_max_load_date': 'datetime64[ns]', # 周最大负荷日期
    'weekly_max_load_time': 'datetime64[ns]', # 周最大负荷时间
    'weekly_min_load': 'float64',       # 周最小负荷
    'weekly_min_load_date': 'datetime64[ns]', # 周最小负荷日期
    'weekly_min_load_time': 'datetime64[ns]', # 周最小负荷时间
    'weekly_avg_load': 'float64',       # 周平均负荷
    'weekly_total_energy': 'float64',   # 周总电量
    'weekly_peak_valley_diff': 'float64', # 周峰谷差
    'weekly_peak_valley_ratio': 'float64', # 周峰谷比
    'weekly_load_factor': 'float64',    # 周负荷率
    'weekday_avg_load': 'float64',      # 工作日平均负荷
    'weekend_avg_load': 'float64',      # 周末平均负荷
    'weekday_weekend_ratio': 'float64', # 工作日周末比
    
    # 元数据
    'created_at': 'datetime64[ns]',     # 创建时间
    'updated_at': 'datetime64[ns]'      # 更新时间
}
```

### 2.2 天气数据格式
```python
weather_data_schema = {
    # 空间维度
    'region': 'string',                 # 区域名称
    'longitude': 'float64',             # 经度
    'latitude': 'float64',              # 纬度
    
    # 时间维度
    'timestamp': 'datetime64[ns]',      # 时间戳（统一为北京时间）
    
    # 气象要素
    'temperature': 'float64',           # 气温(℃)
    'humidity': 'float64',              # 相对湿度(%)
    'pressure': 'float64',              # 气压(hPa)
    'precipitation': 'float64',         # 降水量(mm)
    'dew_point': 'float64',             # 露点温度(℃)
    'wind_speed_u': 'float64',          # 纬向风速(m/s)
    'wind_speed_v': 'float64',          # 经向风速(m/s)
    'wind_speed': 'float64',            # 风速(m/s)
    'wind_direction': 'float64',        # 风向(度)
    'solar_radiation_down': 'float64',  # 太阳辐射总强度(J/m2)
    'solar_radiation_net': 'float64',   # 太阳辐射净强度(J/m2)
    'solar_radiation_direct': 'float64', # 直接辐射(J/m2)
    'uv_intensity': 'float64',          # 紫外强度(J/m2)
    
    # 云量信息
    'cloud_cover_total': 'float64',     # 总云量
    'cloud_cover_low': 'float64',       # 低云量
    'cloud_cover_mid': 'float64',       # 中云量
    'cloud_cover_high': 'float64',      # 高云量
    'cloud_base_height': 'float64',     # 云底高度(m)
    
    # 其他气象要素
    'snow_depth': 'float64',            # 积雪深度(mm)
    'snow_density': 'float64',          # 积雪密度(kg/m3)
    'evaporation': 'float64',           # 蒸发量(mm)
    'potential_evaporation': 'float64', # 潜在蒸发量(mm)
    'surface_temperature': 'float64',   # 地表温度(℃)
    'soil_temperature': 'float64',      # 土壤温度(℃)
    
    # 大气稳定度指标
    'k_index': 'float64',               # K指数
    'cape': 'float64',                  # 对流可用位能(J/kg)
    'thunderstorm_probability': 'float64', # 雷暴概率
    
    # 元数据
    'data_source': 'string',            # 数据源
    'quality_flag': 'int64'             # 质量标志
}
```

## 3. 数据质量规范

### 3.1 缺失值处理
- **负荷数据**: 使用前向填充(ffill)和后向填充(bfill)结合
- **天气数据**: 使用线性插值或邻近站点数据填充
- **时间序列**: 缺失时间点使用插值方法填充

### 3.2 异常值处理
- **IQR方法**: 使用1.5倍IQR识别异常值
- **Z-Score方法**: 使用3倍标准差识别异常值
- **物理约束**: 基于物理意义设置上下限

### 3.3 数据一致性
- **时间格式**: 统一使用北京时间(UTC+8)
- **坐标系统**: 统一使用WGS84坐标系
- **单位统一**: 所有物理量使用标准单位

## 4. 数据存储规范

### 4.1 文件格式
- **主要格式**: Parquet (压缩、列式存储、支持分区)
- **备用格式**: CSV (用于数据交换)
- **元数据**: JSON (配置文件、数据字典)

### 4.2 分区策略
```
data/processed/
├── load_data/
│   ├── year=2023/
│   ├── year=2024/
│   └── year=2025/
├── weather_data/
│   ├── region=双流/
│   ├── region=大邑/
│   └── ...
└── merged_data/
    ├── year=2023/
    ├── year=2024/
    └── year=2025/
```

### 4.3 压缩设置
- **压缩算法**: Snappy (快速压缩/解压)
- **行组大小**: 128MB
- **页面大小**: 1MB

## 5. 数据版本控制

### 5.1 版本命名
```
v1.0.0 - 初始数据版本
v1.1.0 - 数据清洗优化
v1.2.0 - 特征工程完成
v2.0.0 - 模型训练数据
```

### 5.2 变更记录
- 记录每次数据处理的变更内容
- 保存原始数据和处理脚本
- 建立数据血缘关系

## 6. 数据访问接口

### 6.1 数据加载函数
```python
def load_processed_data(data_type, start_date=None, end_date=None, regions=None):
    """加载处理后的数据"""
    pass

def load_merged_data(start_date=None, end_date=None, regions=None):
    """加载合并后的大宽表"""
    pass
```

### 6.2 数据验证函数
```python
def validate_data_schema(df, schema):
    """验证数据格式是否符合规范"""
    pass

def check_data_quality(df):
    """检查数据质量"""
    pass
``` 