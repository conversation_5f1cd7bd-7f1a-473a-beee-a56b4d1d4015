{"cells": [{"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["# 空调负荷柔性调控能力分析系统 - 模型构建\n", "\n", "本notebook用于构建空调负荷识别模型和柔性调控能力评估模型，包括数据集划分、模型训练、评估和调优。\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 导入必要的库\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import os\n", "from pathlib import Path\n", "import warnings\n", "import pickle\n", "import joblib\n", "from datetime import datetime, timedelta\n", "\n", "# 机器学习库\n", "from sklearn.preprocessing import StandardScaler, MinMaxScaler, OneHotEncoder\n", "from sklearn.model_selection import train_test_split, GridSearchCV, TimeSeriesSplit\n", "from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error\n", "from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor\n", "from sklearn.linear_model import LinearRegression, Ridge, Lasso\n", "from sklearn.svm import SVR\n", "from sklearn.pipeline import Pipeline\n", "from sklearn.compose import ColumnTransformer\n", "from sklearn.impute import SimpleImputer\n", "from sklearn.feature_selection import SelectFromModel\n", "import xgboost as xgb\n", "import lightgbm as lgb\n", "\n", "# 设置中文显示\n", "plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签\n", "plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号\n", "warnings.filterwarnings('ignore')\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 定义数据路径\n", "DATA_DIR = Path(\"../data\")\n", "PROCESSED_DATA_DIR = DATA_DIR / \"processed\"\n", "MODELS_DIR = Path(\"../models\")\n", "\n", "# 确保目录存在\n", "PROCESSED_DATA_DIR.mkdir(parents=True, exist_ok=True)\n", "MODELS_DIR.mkdir(parents=True, exist_ok=True)\n", "\n", "print(f\"处理后数据目录: {PROCESSED_DATA_DIR}\")\n", "print(f\"模型目录: {MODELS_DIR}\")\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 1. 加载特征工程后的数据\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 加载特征工程后的数据\n", "def load_data(file_path, file_type='parquet'):\n", "    \"\"\"加载数据文件\"\"\"\n", "    try:\n", "        if file_type == 'parquet':\n", "            if Path(file_path).with_suffix('.parquet').exists():\n", "                return pd.read_parquet(Path(file_path).with_suffix('.parquet'))\n", "        elif file_type == 'csv':\n", "            if Path(file_path).with_suffix('.csv').exists():\n", "                return pd.read_csv(Path(file_path).with_suffix('.csv'))\n", "        \n", "        print(f\"文件不存在: {file_path}\")\n", "        return pd.DataFrame()\n", "    except Exception as e:\n", "        print(f\"加载数据时出错: {e}\")\n", "        return pd.DataFrame()\n", "\n", "# 加载特征工程后的数据\n", "features_df = load_data(PROCESSED_DATA_DIR / \"features_engineered\", file_type='parquet')\n", "\n", "# 如果没有特征工程后的数据，则加载原始数据\n", "if features_df.empty:\n", "    print(\"未找到特征工程后的数据，加载原始数据...\")\n", "    # 尝试加载合并后的数据\n", "    features_df = load_data(PROCESSED_DATA_DIR / \"load_weather_regional_merged\", file_type='parquet')\n", "    \n", "    # 如果没有合并后的数据，则分别加载负荷数据和天气数据\n", "    if features_df.empty:\n", "        print(\"未找到合并后的数据，加载原始负荷数据...\")\n", "        features_df = load_data(DATA_DIR / \"raw\" / \"load_data\", file_type='parquet')\n", "\n", "# 显示数据基本信息\n", "if not features_df.empty:\n", "    print(f\"数据维度: {features_df.shape}\")\n", "    print(f\"列名: {features_df.columns.tolist()}\")\n", "    display(features_df.head())\n", "    \n", "    # 检查缺失值\n", "    missing_values = features_df.isnull().sum()\n", "    missing_percent = (missing_values / len(features_df)) * 100\n", "    \n", "    missing_df = pd.DataFrame({\n", "        '缺失值数量': missing_values,\n", "        '缺失百分比': missing_percent\n", "    })\n", "    \n", "    print(\"\\n缺失值情况:\")\n", "    display(missing_df[missing_df['缺失值数量'] > 0])\n", "else:\n", "    print(\"没有可用的数据\")\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 2. 数据预处理与划分\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 数据预处理与划分\n", "def preprocess_and_split_data(df, target_col='load_value', test_size=0.2, val_size=0.25, random_state=42):\n", "    \"\"\"\n", "    数据预处理与划分\n", "    \n", "    Args:\n", "        df: 输入数据DataFrame\n", "        target_col: 目标列名\n", "        test_size: 测试集比例\n", "        val_size: 验证集比例（相对于训练集）\n", "        random_state: 随机种子\n", "        \n", "    Returns:\n", "        X_train, X_val, X_test, y_train, y_val, y_test, feature_names\n", "    \"\"\"\n", "    if df is None or df.empty:\n", "        print(\"没有数据可处理\")\n", "        return None, None, None, None, None, None, None\n", "    \n", "    if target_col not in df.columns:\n", "        print(f\"目标列 {target_col} 不存在\")\n", "        return None, None, None, None, None, None, None\n", "    \n", "    # 创建副本避免修改原始数据\n", "    data = df.copy()\n", "    \n", "    # 1. 处理缺失值\n", "    # 删除缺失值过多的列\n", "    missing_threshold = 0.3  # 缺失值比例阈值\n", "    missing_cols = []\n", "    \n", "    for col in data.columns:\n", "        missing_ratio = data[col].isnull().mean()\n", "        if missing_ratio > missing_threshold:\n", "            missing_cols.append(col)\n", "    \n", "    if missing_cols:\n", "        print(f\"删除缺失值过多的列: {missing_cols}\")\n", "        data = data.drop(columns=missing_cols)\n", "    \n", "    # 2. 处理非数值特征\n", "    # 识别非数值列\n", "    non_numeric_cols = data.select_dtypes(exclude=['number']).columns.tolist()\n", "    \n", "    # 排除时间列和目标列\n", "    exclude_cols = ['timestamp', 'time', 'date', target_col]\n", "    non_numeric_cols = [col for col in non_numeric_cols if col not in exclude_cols]\n", "    \n", "    # 处理非数值列\n", "    if non_numeric_cols:\n", "        print(f\"非数值列: {non_numeric_cols}\")\n", "        \n", "        # 对于少量唯一值的列，使用独热编码\n", "        for col in non_numeric_cols:\n", "            if data[col].nunique() < 10:  # 唯一值较少\n", "                # 创建独热编码\n", "                dummies = pd.get_dummies(data[col], prefix=col, drop_first=True)\n", "                # 合并到原始数据\n", "                data = pd.concat([data, dummies], axis=1)\n", "            \n", "            # 删除原始列\n", "            data = data.drop(columns=[col])\n", "    \n", "    # 3. 分离特征和目标\n", "    # 排除不用于训练的列\n", "    exclude_cols = ['timestamp', 'time', 'date', target_col]\n", "    feature_cols = [col for col in data.columns if col not in exclude_cols]\n", "    \n", "    X = data[feature_cols]\n", "    y = data[target_col]\n", "    \n", "    # 4. 数据集划分\n", "    # 首先划分出测试集\n", "    X_train_val, X_test, y_train_val, y_test = train_test_split(\n", "        X, y, test_size=test_size, random_state=random_state\n", "    )\n", "    \n", "    # 从剩余数据中划分出验证集\n", "    X_train, X_val, y_train, y_val = train_test_split(\n", "        X_train_val, y_train_val, test_size=val_size, random_state=random_state\n", "    )\n", "    \n", "    print(f\"训练集: {X_train.shape[0]} 样本\")\n", "    print(f\"验证集: {X_val.shape[0]} 样本\")\n", "    print(f\"测试集: {X_test.shape[0]} 样本\")\n", "    \n", "    return X_train, X_val, X_test, y_train, y_val, y_test, feature_cols\n", "\n", "# 应用数据预处理与划分\n", "if not features_df.empty:\n", "    # 确定目标列名\n", "    target_col = None\n", "    for col in features_df.columns:\n", "        if 'load_value' in col.lower():\n", "            target_col = col\n", "            break\n", "    \n", "    if not target_col:\n", "        for col in features_df.columns:\n", "            if 'load' in col.lower():\n", "                target_col = col\n", "                break\n", "    \n", "    if target_col:\n", "        print(f\"使用目标列: {target_col}\")\n", "        X_train, X_val, X_test, y_train, y_val, y_test, feature_names = preprocess_and_split_data(\n", "            features_df, target_col=target_col\n", "        )\n", "        \n", "        # 显示特征信息\n", "        if X_train is not None:\n", "            print(f\"\\n特征数量: {len(feature_names)}\")\n", "            print(f\"特征列表: {feature_names}\")\n", "    else:\n", "        print(\"未找到目标列\")\n", "else:\n", "    print(\"没有可用的数据\")\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 3. 空调负荷识别模型\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 构建空调负荷识别模型\n", "def build_ac_load_model(X_train, y_train, X_val, y_val):\n", "    \"\"\"\n", "    构建空调负荷识别模型\n", "    \n", "    Args:\n", "        X_train: 训练特征\n", "        y_train: 训练目标\n", "        X_val: 验证特征\n", "        y_val: 验证目标\n", "        \n", "    Returns:\n", "        最佳模型\n", "    \"\"\"\n", "    if X_train is None or y_train is None:\n", "        print(\"没有训练数据\")\n", "        return None\n", "    \n", "    # 创建模型列表\n", "    models = {\n", "        'Linear Regression': LinearRegression(),\n", "        'Ridge': Ridge(),\n", "        'Random Forest': RandomForestRegressor(random_state=42),\n", "        'Gradient Boosting': GradientBoostingRegressor(random_state=42),\n", "        'XGBoost': xgb.XGBRegressor(random_state=42),\n", "        'LightGBM': lgb.LGBMRegressor(random_state=42)\n", "    }\n", "    \n", "    # 评估结果\n", "    results = {}\n", "    \n", "    # 评估每个模型\n", "    for name, model in models.items():\n", "        print(f\"\\n训练模型: {name}\")\n", "        \n", "        # 训练模型\n", "        model.fit(X_train, y_train)\n", "        \n", "        # 预测\n", "        train_pred = model.predict(X_train)\n", "        val_pred = model.predict(X_val)\n", "        \n", "        # 评估\n", "        train_rmse = np.sqrt(mean_squared_error(y_train, train_pred))\n", "        val_rmse = np.sqrt(mean_squared_error(y_val, val_pred))\n", "        \n", "        train_r2 = r2_score(y_train, train_pred)\n", "        val_r2 = r2_score(y_val, val_pred)\n", "        \n", "        # 保存结果\n", "        results[name] = {\n", "            'model': model,\n", "            'train_rmse': train_rmse,\n", "            'val_rmse': val_rmse,\n", "            'train_r2': train_r2,\n", "            'val_r2': val_r2\n", "        }\n", "        \n", "        print(f\"  训练集 RMSE: {train_rmse:.4f}, R²: {train_r2:.4f}\")\n", "        print(f\"  验证集 RMSE: {val_rmse:.4f}, R²: {val_r2:.4f}\")\n", "    \n", "    # 选择最佳模型\n", "    best_model_name = min(results, key=lambda x: results[x]['val_rmse'])\n", "    best_model = results[best_model_name]['model']\n", "    \n", "    print(f\"\\n最佳模型: {best_model_name}\")\n", "    print(f\"  验证集 RMSE: {results[best_model_name]['val_rmse']:.4f}\")\n", "    print(f\"  验证集 R²: {results[best_model_name]['val_r2']:.4f}\")\n", "    \n", "    # 特征重要性（如果模型支持）\n", "    if hasattr(best_model, 'feature_importances_'):\n", "        feature_importances = pd.DataFrame({\n", "            'feature': X_train.columns,\n", "            'importance': best_model.feature_importances_\n", "        }).sort_values('importance', ascending=False)\n", "        \n", "        print(\"\\n特征重要性 (前10):\")\n", "        display(feature_importances.head(10))\n", "    \n", "    return best_model, results\n", "\n", "# 应用空调负荷识别模型\n", "if 'X_train' in locals() and X_train is not None:\n", "    best_model, model_results = build_ac_load_model(X_train, y_train, X_val, y_val)\n", "    \n", "    # 评估最佳模型在测试集上的表现\n", "    if best_model is not None and X_test is not None:\n", "        test_pred = best_model.predict(X_test)\n", "        test_rmse = np.sqrt(mean_squared_error(y_test, test_pred))\n", "        test_r2 = r2_score(y_test, test_pred)\n", "        \n", "        print(f\"\\n测试集评估:\")\n", "        print(f\"  RMSE: {test_rmse:.4f}\")\n", "        print(f\"  R²: {test_r2:.4f}\")\n", "        \n", "        # 可视化预测结果\n", "        plt.figure(figsize=(12, 6))\n", "        \n", "        # 实际值与预测值对比\n", "        plt.scatter(y_test, test_pred, alpha=0.5)\n", "        plt.plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'r--')\n", "        plt.xlabel('实际值')\n", "        plt.ylabel('预测值')\n", "        plt.title('实际值 vs 预测值')\n", "        plt.grid(True)\n", "        plt.show()\n", "        \n", "        # 残差分析\n", "        residuals = y_test - test_pred\n", "        \n", "        plt.figure(figsize=(12, 6))\n", "        plt.scatter(test_pred, residuals, alpha=0.5)\n", "        plt.axhline(y=0, color='r', linestyle='--')\n", "        plt.xlabel('预测值')\n", "        plt.ylabel('残差')\n", "        plt.title('残差分析')\n", "        plt.grid(True)\n", "        plt.show()\n", "        \n", "        # 保存最佳模型\n", "        model_file = MODELS_DIR / \"ac_load_model.pkl\"\n", "        \n", "        try:\n", "            joblib.dump(best_model, model_file)\n", "            print(f\"\\n模型已保存: {model_file}\")\n", "        except Exception as e:\n", "            print(f\"保存模型时出错: {e}\")\n", "else:\n", "    print(\"没有可用的训练数据\")\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 4. 柔性调控能力评估模型\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 柔性调控能力评估模型\n", "def estimate_flexibility(df, load_model=None, temp_col=None, load_col=None):\n", "    \"\"\"\n", "    评估空调负荷柔性调控能力\n", "    \n", "    Args:\n", "        df: 输入数据DataFrame\n", "        load_model: 空调负荷识别模型\n", "        temp_col: 温度列名\n", "        load_col: 负荷列名\n", "        \n", "    Returns:\n", "        包含柔性调控能力评估的DataFrame\n", "    \"\"\"\n", "    if df is None or df.empty:\n", "        print(\"没有数据可处理\")\n", "        return df\n", "    \n", "    # 创建副本避免修改原始数据\n", "    result_df = df.copy()\n", "    \n", "    # 确定温度列名\n", "    if temp_col is None:\n", "        for col in result_df.columns:\n", "            if 'temperature' in col.lower() or 'temp' in col.lower():\n", "                temp_col = col\n", "                break\n", "    \n", "    # 确定负荷列名\n", "    if load_col is None:\n", "        for col in result_df.columns:\n", "            if 'load_value' in col.lower() or 'load' in col.lower():\n", "                load_col = col\n", "                break\n", "    \n", "    if temp_col is None or load_col is None:\n", "        print(\"未找到温度列或负荷列\")\n", "        return result_df\n", "    \n", "    print(f\"使用温度列: {temp_col}\")\n", "    print(f\"使用负荷列: {load_col}\")\n", "    \n", "    # 1. 基于温度敏感度的柔性调控能力评估\n", "    # 设置温度阈值\n", "    cooling_threshold = 26  # 制冷阈值\n", "    heating_threshold = 18  # 制热阈值\n", "    \n", "    # 计算空调负荷比例\n", "    # 制冷负荷比例 - 温度高于阈值时\n", "    result_df['cooling_load_ratio'] = np.where(\n", "        result_df[temp_col] > cooling_threshold,\n", "        (result_df[temp_col] - cooling_threshold) / result_df[temp_col] * 0.6,  # 假设60%为空调负荷\n", "        0\n", "    )\n", "    \n", "    # 制热负荷比例 - 温度低于阈值时\n", "    result_df['heating_load_ratio'] = np.where(\n", "        result_df[temp_col] < heating_threshold,\n", "        (heating_threshold - result_df[temp_col]) / heating_threshold * 0.5,  # 假设50%为空调负荷\n", "        0\n", "    )\n", "    \n", "    # 计算空调负荷\n", "    result_df['ac_load_estimated'] = result_df[load_col] * (result_df['cooling_load_ratio'] + result_df['heating_load_ratio'])\n", "    \n", "    # 2. 如果有空调负荷识别模型，使用模型预测\n", "    if load_model is not None:\n", "        # 准备特征\n", "        feature_cols = [col for col in result_df.columns if col not in [load_col, 'timestamp', 'time', 'date']]\n", "        \n", "        try:\n", "            # 预测空调负荷\n", "            result_df['ac_load_predicted'] = load_model.predict(result_df[feature_cols])\n", "            \n", "            # 确保预测值非负\n", "            result_df['ac_load_predicted'] = result_df['ac_load_predicted'].clip(lower=0)\n", "            \n", "            # 计算空调负荷比例\n", "            result_df['ac_load_ratio_predicted'] = result_df['ac_load_predicted'] / result_df[load_col]\n", "            result_df['ac_load_ratio_predicted'] = result_df['ac_load_ratio_predicted'].clip(lower=0, upper=1)\n", "        except Exception as e:\n", "            print(f\"模型预测时出错: {e}\")\n", "    \n", "    # 3. 计算柔性调控能力\n", "    # 柔性调控能力 = 空调负荷 * 可调控比例\n", "    # 假设制冷空调可调控比例为30%，制热空调可调控比例为25%\n", "    cooling_control_ratio = 0.3\n", "    heating_control_ratio = 0.25\n", "    \n", "    # 计算柔性调控能力\n", "    result_df['flexibility_cooling'] = result_df['ac_load_estimated'] * result_df['cooling_load_ratio'] / (result_df['cooling_load_ratio'] + result_df['heating_load_ratio'] + 1e-10) * cooling_control_ratio\n", "    result_df['flexibility_heating'] = result_df['ac_load_estimated'] * result_df['heating_load_ratio'] / (result_df['cooling_load_ratio'] + result_df['heating_load_ratio'] + 1e-10) * heating_control_ratio\n", "    \n", "    # 总柔性调控能力\n", "    result_df['flexibility_total'] = result_df['flexibility_cooling'] + result_df['flexibility_heating']\n", "    \n", "    # 柔性调控能力比例\n", "    result_df['flexibility_ratio'] = result_df['flexibility_total'] / result_df[load_col]\n", "    \n", "    # 4. 根据时间和温度特征调整柔性调控能力\n", "    # 高峰时段调整系数\n", "    if 'hour' in result_df.columns:\n", "        # 早高峰 (7-9点) 和晚高峰 (18-20点) 调整系数\n", "        result_df['peak_factor'] = np.where(\n", "            (result_df['hour'].between(7, 9)) | (result_df['hour'].between(18, 20)),\n", "            1.2,  # 高峰时段增加20%\n", "            1.0\n", "        )\n", "        \n", "        # 应用调整系数\n", "        result_df['flexibility_total_adjusted'] = result_df['flexibility_total'] * result_df['peak_factor']\n", "    else:\n", "        result_df['flexibility_total_adjusted'] = result_df['flexibility_total']\n", "    \n", "    # 5. 按区域和时间统计柔性调控能力\n", "    if 'region' in result_df.columns:\n", "        # 计算每个区域的平均柔性调控能力\n", "        region_flexibility = result_df.groupby('region')['flexibility_total_adjusted'].mean().reset_index()\n", "        region_flexibility.columns = ['region', 'avg_flexibility']\n", "        \n", "        # 排序\n", "        region_flexibility = region_flexibility.sort_values('avg_flexibility', ascending=False)\n", "        \n", "        print(\"\\n区域柔性调控能力排名:\")\n", "        display(region_flexibility)\n", "    \n", "    if 'hour' in result_df.columns:\n", "        # 计算每个小时的平均柔性调控能力\n", "        hour_flexibility = result_df.groupby('hour')['flexibility_total_adjusted'].mean().reset_index()\n", "        hour_flexibility.columns = ['hour', 'avg_flexibility']\n", "        \n", "        # 排序\n", "        hour_flexibility = hour_flexibility.sort_values('avg_flexibility', ascending=False)\n", "        \n", "        print(\"\\n时段柔性调控能力排名:\")\n", "        display(hour_flexibility)\n", "    \n", "    return result_df\n", "\n", "# 应用柔性调控能力评估\n", "if not features_df.empty and 'best_model' in locals() and best_model is not None:\n", "    # 确定温度列名和负荷列名\n", "    temp_col = None\n", "    load_col = None\n", "    \n", "    for col in features_df.columns:\n", "        if 'temperature' in col.lower() or 'temp' in col.lower():\n", "            temp_col = col\n", "            break\n", "    \n", "    for col in features_df.columns:\n", "        if 'load_value' in col.lower() or 'load' in col.lower():\n", "            load_col = col\n", "            break\n", "    \n", "    if temp_col is not None and load_col is not None:\n", "        # 评估柔性调控能力\n", "        flexibility_df = estimate_flexibility(features_df, load_model=best_model, temp_col=temp_col, load_col=load_col)\n", "        \n", "        # 可视化柔性调控能力\n", "        if 'flexibility_total_adjusted' in flexibility_df.columns:\n", "            plt.figure(figsize=(12, 6))\n", "            \n", "            # 负荷和柔性调控能力对比\n", "            plt.plot(flexibility_df[load_col].values[:100], label='总负荷')\n", "            plt.plot(flexibility_df['ac_load_estimated'].values[:100], label='空调负荷')\n", "            plt.plot(flexibility_df['flexibility_total_adjusted'].values[:100], label='柔性调控能力')\n", "            \n", "            plt.xlabel('样本')\n", "            plt.ylabel('负荷/柔性调控能力')\n", "            plt.title('负荷和柔性调控能力对比')\n", "            plt.legend()\n", "            plt.grid(True)\n", "            plt.show()\n", "            \n", "            # 保存柔性调控能力评估结果\n", "            output_file = PROCESSED_DATA_DIR / \"flexibility_assessment\"\n", "            \n", "            try:\n", "                # 保存为CSV\n", "                flexibility_df.to_csv(output_file.with_suffix('.csv'), index=False, encoding='utf-8')\n", "                print(f\"已保存CSV: {output_file.with_suffix('.csv')}\")\n", "                \n", "                # 保存为Parquet\n", "                flexibility_df.to_parquet(output_file.with_suffix('.parquet'), index=False)\n", "                print(f\"已保存Parquet: {output_file.with_suffix('.parquet')}\")\n", "            except Exception as e:\n", "                print(f\"保存数据时出错: {e}\")\n", "    else:\n", "        print(\"未找到温度列或负荷列，无法评估柔性调控能力\")\n", "else:\n", "    print(\"没有可用的数据或模型，无法评估柔性调控能力\")\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 5. 模型调优\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 模型调优\n", "def tune_model(X_train, y_train, X_val, y_val, model_type='xgboost'):\n", "    \"\"\"\n", "    模型调优\n", "    \n", "    Args:\n", "        X_train: 训练特征\n", "        y_train: 训练目标\n", "        X_val: 验证特征\n", "        y_val: 验证目标\n", "        model_type: 模型类型\n", "        \n", "    Returns:\n", "        调优后的最佳模型\n", "    \"\"\"\n", "    if X_train is None or y_train is None:\n", "        print(\"没有训练数据\")\n", "        return None\n", "    \n", "    # 创建时间序列交叉验证\n", "    tscv = TimeSeriesSplit(n_splits=5)\n", "    \n", "    if model_type == 'xgboost':\n", "        # XGBoost模型调优\n", "        print(\"调优XGBoost模型...\")\n", "        \n", "        # 定义参数网格\n", "        param_grid = {\n", "            'n_estimators': [100, 200, 300],\n", "            'max_depth': [3, 5, 7],\n", "            'learning_rate': [0.01, 0.05, 0.1],\n", "            'subsample': [0.8, 0.9, 1.0],\n", "            'colsample_bytree': [0.8, 0.9, 1.0]\n", "        }\n", "        \n", "        # 创建模型\n", "        model = xgb.XGBRegressor(random_state=42)\n", "        \n", "    elif model_type == 'lightgbm':\n", "        # LightGBM模型调优\n", "        print(\"调优LightGBM模型...\")\n", "        \n", "        # 定义参数网格\n", "        param_grid = {\n", "            'n_estimators': [100, 200, 300],\n", "            'max_depth': [3, 5, 7],\n", "            'learning_rate': [0.01, 0.05, 0.1],\n", "            'num_leaves': [31, 63, 127],\n", "            'subsample': [0.8, 0.9, 1.0]\n", "        }\n", "        \n", "        # 创建模型\n", "        model = lgb.LGBMRegressor(random_state=42)\n", "        \n", "    elif model_type == 'random_forest':\n", "        # 随机森林模型调优\n", "        print(\"调优随机森林模型...\")\n", "        \n", "        # 定义参数网格\n", "        param_grid = {\n", "            'n_estimators': [100, 200, 300],\n", "            'max_depth': [None, 10, 20],\n", "            'min_samples_split': [2, 5, 10],\n", "            'min_samples_leaf': [1, 2, 4]\n", "        }\n", "        \n", "        # 创建模型\n", "        model = RandomForestRegressor(random_state=42)\n", "        \n", "    else:\n", "        print(f\"不支持的模型类型: {model_type}\")\n", "        return None\n", "    \n", "    # 创建网格搜索\n", "    grid_search = GridSearchCV(\n", "        estimator=model,\n", "        param_grid=param_grid,\n", "        cv=tscv,\n", "        scoring='neg_root_mean_squared_error',\n", "        n_jobs=-1,\n", "        verbose=1\n", "    )\n", "    \n", "    # 执行网格搜索\n", "    grid_search.fit(X_train, y_train)\n", "    \n", "    # 获取最佳模型\n", "    best_model = grid_search.best_estimator_\n", "    best_params = grid_search.best_params_\n", "    \n", "    print(f\"\\n最佳参数: {best_params}\")\n", "    \n", "    # 评估最佳模型\n", "    train_pred = best_model.predict(X_train)\n", "    val_pred = best_model.predict(X_val)\n", "    \n", "    train_rmse = np.sqrt(mean_squared_error(y_train, train_pred))\n", "    val_rmse = np.sqrt(mean_squared_error(y_val, val_pred))\n", "    \n", "    train_r2 = r2_score(y_train, train_pred)\n", "    val_r2 = r2_score(y_val, val_pred)\n", "    \n", "    print(f\"\\n调优后的模型性能:\")\n", "    print(f\"  训练集 RMSE: {train_rmse:.4f}, R²: {train_r2:.4f}\")\n", "    print(f\"  验证集 RMSE: {val_rmse:.4f}, R²: {val_r2:.4f}\")\n", "    \n", "    # 特征重要性\n", "    if hasattr(best_model, 'feature_importances_'):\n", "        feature_importances = pd.DataFrame({\n", "            'feature': X_train.columns,\n", "            'importance': best_model.feature_importances_\n", "        }).sort_values('importance', ascending=False)\n", "        \n", "        print(\"\\n特征重要性 (前10):\")\n", "        display(feature_importances.head(10))\n", "    \n", "    return best_model\n", "\n", "# 应用模型调优\n", "# 注意：模型调优可能需要较长时间，可以根据需要取消注释以下代码\n", "print(\"模型调优可能需要较长时间，请取消注释以下代码以执行调优\")\n", "\n", "# if 'X_train' in locals() and X_train is not None:\n", "#     # 选择要调优的模型类型\n", "#     model_type = 'xgboost'  # 'xgboost', 'lightgbm', 'random_forest'\n", "#     \n", "#     # 执行模型调优\n", "#     tuned_model = tune_model(X_train, y_train, X_val, y_val, model_type=model_type)\n", "#     \n", "#     # 评估调优后的模型在测试集上的表现\n", "#     if tuned_model is not None and X_test is not None:\n", "#         test_pred = tuned_model.predict(X_test)\n", "#         test_rmse = np.sqrt(mean_squared_error(y_test, test_pred))\n", "#         test_r2 = r2_score(y_test, test_pred)\n", "#         \n", "#         print(f\"\\n测试集评估:\")\n", "#         print(f\"  RMSE: {test_rmse:.4f}\")\n", "#         print(f\"  R²: {test_r2:.4f}\")\n", "#         \n", "#         # 保存调优后的模型\n", "#         tuned_model_file = MODELS_DIR / f\"ac_load_model_tuned_{model_type}.pkl\"\n", "#         \n", "#         try:\n", "#             joblib.dump(tuned_model, tuned_model_file)\n", "#             print(f\"\\n调优后的模型已保存: {tuned_model_file}\")\n", "#         except Exception as e:\n", "#             print(f\"保存模型时出错: {e}\")\n", "# else:\n", "#     print(\"没有可用的训练数据\")\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 6. 后续步骤\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "完成模型构建后，后续步骤包括：\n", "\n", "1. **结果可视化与报告**\n", "   - 创建可视化与报告notebook\n", "   - 空调负荷时空分布可视化\n", "   - 柔性调控能力评估报告\n", "   - 决策支持仪表板\n", "\n", "2. **模型部署与应用**\n", "   - 模型部署到生产环境\n", "   - 实时数据接入与预测\n", "   - 柔性调控策略制定\n", "   - 效果评估与反馈\n", "\n", "3. **进一步优化**\n", "   - 模型集成与融合\n", "   - 特征工程优化\n", "   - 深度学习方法尝试\n", "   - 时空特征深入挖掘\n", "\"\"\"\n"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}