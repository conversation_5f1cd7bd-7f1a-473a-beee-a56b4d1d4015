{"cells": [{"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["# 空调负荷柔性调控能力分析系统 - 特征工程\n", "\n", "本notebook用于对合并后的负荷和天气数据进行特征工程，提取时间特征、温度敏感度特征、负荷特征等，为后续模型构建做准备。\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 导入必要的库\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import os\n", "from pathlib import Path\n", "import warnings\n", "import re\n", "from datetime import datetime, timedelta\n", "from sklearn.preprocessing import StandardScaler, MinMaxScaler\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.metrics import mean_squared_error, r2_score\n", "\n", "# 设置中文显示\n", "plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签\n", "plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号\n", "warnings.filterwarnings('ignore')\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 定义数据路径\n", "DATA_DIR = Path(\"../data\")\n", "RAW_DATA_DIR = DATA_DIR / \"raw\"\n", "PROCESSED_DATA_DIR = DATA_DIR / \"processed\"\n", "MODELS_DIR = Path(\"../models\")\n", "\n", "# 确保目录存在\n", "PROCESSED_DATA_DIR.mkdir(parents=True, exist_ok=True)\n", "MODELS_DIR.mkdir(parents=True, exist_ok=True)\n", "\n", "print(f\"原始数据目录: {RAW_DATA_DIR}\")\n", "print(f\"处理后数据目录: {PROCESSED_DATA_DIR}\")\n", "print(f\"模型目录: {MODELS_DIR}\")\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 1. 加载合并后的数据\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 加载数据\n", "def load_data(file_path, file_type='parquet'):\n", "    \"\"\"加载数据文件\"\"\"\n", "    try:\n", "        if file_type == 'parquet':\n", "            if Path(file_path).with_suffix('.parquet').exists():\n", "                return pd.read_parquet(Path(file_path).with_suffix('.parquet'))\n", "        elif file_type == 'csv':\n", "            if Path(file_path).with_suffix('.csv').exists():\n", "                return pd.read_csv(Path(file_path).with_suffix('.csv'))\n", "        \n", "        print(f\"文件不存在: {file_path}\")\n", "        return pd.DataFrame()\n", "    except Exception as e:\n", "        print(f\"加载数据时出错: {e}\")\n", "        return pd.DataFrame()\n", "\n", "# 加载合并后的数据\n", "# 注意：如果没有合并后的数据，可以使用原始数据进行特征工程示例\n", "load_weather_df = load_data(PROCESSED_DATA_DIR / \"load_weather_regional_merged\", file_type='parquet')\n", "\n", "# 如果没有合并后的数据，则分别加载负荷数据和天气数据\n", "if load_weather_df.empty:\n", "    print(\"未找到合并后的数据，加载原始数据...\")\n", "    load_df = load_data(RAW_DATA_DIR / \"load_data\", file_type='parquet')\n", "    weather_regional_df = load_data(PROCESSED_DATA_DIR / \"weather_regional_merged\", file_type='parquet')\n", "    \n", "    # 显示数据基本信息\n", "    print(\"\\n负荷数据:\")\n", "    if not load_df.empty:\n", "        print(f\"- 维度: {load_df.shape}\")\n", "        print(f\"- 列名: {load_df.columns.tolist()}\")\n", "        display(load_df.head())\n", "    else:\n", "        print(\"- 数据为空\")\n", "    \n", "    print(\"\\n区域天气数据:\")\n", "    if not weather_regional_df.empty:\n", "        print(f\"- 维度: {weather_regional_df.shape}\")\n", "        print(f\"- 列名: {weather_regional_df.columns.tolist()}\")\n", "        display(weather_regional_df.head())\n", "    else:\n", "        print(\"- 数据为空\")\n", "else:\n", "    # 显示合并后的数据\n", "    print(\"\\n负荷-天气合并数据:\")\n", "    print(f\"- 维度: {load_weather_df.shape}\")\n", "    print(f\"- 列名: {load_weather_df.columns.tolist()}\")\n", "    display(load_weather_df.head())\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 2. 时间特征提取\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 提取时间特征\n", "def extract_time_features(df, time_col):\n", "    \"\"\"\n", "    从时间列提取时间特征\n", "    \n", "    Args:\n", "        df: 数据DataFrame\n", "        time_col: 时间列名\n", "        \n", "    Returns:\n", "        添加了时间特征的DataFrame\n", "    \"\"\"\n", "    if df is None or df.empty:\n", "        print(\"没有数据可处理\")\n", "        return df\n", "    \n", "    if time_col not in df.columns:\n", "        print(f\"时间列 {time_col} 不存在\")\n", "        return df\n", "    \n", "    # 创建副本避免修改原始数据\n", "    result_df = df.copy()\n", "    \n", "    # 确保时间列是datetime类型\n", "    if result_df[time_col].dtype != 'datetime64[ns]':\n", "        try:\n", "            result_df[time_col] = pd.to_datetime(result_df[time_col], errors='coerce')\n", "        except Exception as e:\n", "            print(f\"转换时间格式时出错: {e}\")\n", "            return df\n", "    \n", "    # 提取时间特征\n", "    result_df['year'] = result_df[time_col].dt.year\n", "    result_df['month'] = result_df[time_col].dt.month\n", "    result_df['day'] = result_df[time_col].dt.day\n", "    result_df['hour'] = result_df[time_col].dt.hour\n", "    result_df['dayofweek'] = result_df[time_col].dt.dayofweek  # 0-6, 0是星期一\n", "    result_df['is_weekend'] = result_df['dayofweek'].isin([5, 6]).astype(int)  # 5,6是周六日\n", "    \n", "    # 季节特征 (1=春, 2=夏, 3=秋, 4=冬)\n", "    result_df['season'] = result_df['month'].apply(\n", "        lambda x: 1 if 3 <= x <= 5 else 2 if 6 <= x <= 8 else 3 if 9 <= x <= 11 else 4\n", "    )\n", "    \n", "    # 一天中的时段\n", "    result_df['day_period'] = result_df['hour'].apply(\n", "        lambda x: 1 if 6 <= x < 12 else 2 if 12 <= x < 18 else 3 if 18 <= x < 22 else 0\n", "    )  # 0=夜间, 1=上午, 2=下午, 3=晚上\n", "    \n", "    # 工作日/休息日特征\n", "    result_df['is_workday'] = (~result_df['is_weekend']).astype(int)\n", "    \n", "    # 月初/月中/月末特征\n", "    result_df['month_period'] = result_df['day'].apply(\n", "        lambda x: 1 if x <= 10 else 2 if x <= 20 else 3\n", "    )  # 1=月初, 2=月中, 3=月末\n", "    \n", "    # 季节性特征 - 使用正弦和余弦变换捕捉周期性\n", "    # 小时周期性\n", "    result_df['hour_sin'] = np.sin(2 * np.pi * result_df['hour'] / 24)\n", "    result_df['hour_cos'] = np.cos(2 * np.pi * result_df['hour'] / 24)\n", "    \n", "    # 日周期性\n", "    result_df['day_sin'] = np.sin(2 * np.pi * result_df['day'] / 31)\n", "    result_df['day_cos'] = np.cos(2 * np.pi * result_df['day'] / 31)\n", "    \n", "    # 月周期性\n", "    result_df['month_sin'] = np.sin(2 * np.pi * result_df['month'] / 12)\n", "    result_df['month_cos'] = np.cos(2 * np.pi * result_df['month'] / 12)\n", "    \n", "    # 周周期性\n", "    result_df['week_sin'] = np.sin(2 * np.pi * result_df['dayofweek'] / 7)\n", "    result_df['week_cos'] = np.cos(2 * np.pi * result_df['dayofweek'] / 7)\n", "    \n", "    return result_df\n", "\n", "# 应用时间特征提取\n", "# 根据实际数据选择正确的时间列\n", "time_col = 'timestamp' if 'timestamp' in load_weather_df.columns else 'time'\n", "\n", "if not load_weather_df.empty and time_col in load_weather_df.columns:\n", "    # 使用合并后的数据\n", "    df_with_time_features = extract_time_features(load_weather_df, time_col)\n", "    print(f\"已提取时间特征，新增列: {[col for col in df_with_time_features.columns if col not in load_weather_df.columns]}\")\n", "    display(df_with_time_features.head())\n", "elif 'load_df' in locals() and not load_df.empty:\n", "    # 使用负荷数据\n", "    # 首先标准化时间列\n", "    if 'load_date' in load_df.columns and 'load_hour' in load_df.columns:\n", "        load_df['timestamp'] = pd.to_datetime(load_df['load_date']) + pd.to_timedelta(load_df['load_hour'], unit='h')\n", "    \n", "    df_with_time_features = extract_time_features(load_df, 'timestamp')\n", "    print(f\"已提取时间特征，新增列: {[col for col in df_with_time_features.columns if col not in load_df.columns]}\")\n", "    display(df_with_time_features.head())\n", "else:\n", "    print(\"没有合适的数据进行时间特征提取\")\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 3. 温度敏感度特征\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 创建温度敏感度特征\n", "def create_temperature_sensitivity_features(df):\n", "    \"\"\"\n", "    创建温度敏感度相关特征\n", "    \n", "    Args:\n", "        df: 包含负荷和温度数据的DataFrame\n", "        \n", "    Returns:\n", "        添加了温度敏感度特征的DataFrame\n", "    \"\"\"\n", "    if df is None or df.empty:\n", "        print(\"没有数据可处理\")\n", "        return df\n", "    \n", "    # 确定温度列名\n", "    temp_col = None\n", "    load_col = None\n", "    \n", "    # 查找温度列\n", "    for col in df.columns:\n", "        if 'temperature' in col.lower() or 'temp' in col.lower():\n", "            temp_col = col\n", "            break\n", "    \n", "    # 查找负荷列\n", "    for col in df.columns:\n", "        if 'load_value' in col.lower() or 'load' in col.lower():\n", "            load_col = col\n", "            break\n", "    \n", "    if temp_col is None or load_col is None:\n", "        print(f\"未找到温度列或负荷列\")\n", "        return df\n", "    \n", "    print(f\"使用温度列: {temp_col}\")\n", "    print(f\"使用负荷列: {load_col}\")\n", "    \n", "    # 创建副本避免修改原始数据\n", "    result_df = df.copy()\n", "    \n", "    # 确保温度和负荷列是数值类型\n", "    try:\n", "        result_df[temp_col] = pd.to_numeric(result_df[temp_col], errors='coerce')\n", "        result_df[load_col] = pd.to_numeric(result_df[load_col], errors='coerce')\n", "    except Exception as e:\n", "        print(f\"转换数值类型时出错: {e}\")\n", "        return df\n", "    \n", "    # 创建温度敏感度特征\n", "    \n", "    # 1. 温度平方项 - 捕捉非线性关系\n", "    result_df['temperature_squared'] = result_df[temp_col] ** 2\n", "    \n", "    # 2. 冷热度日 (CDD/HDD)\n", "    # 设置基准温度（可根据实际情况调整）\n", "    base_temp_cooling = 26  # 制冷基准温度\n", "    base_temp_heating = 18  # 制热基准温度\n", "    \n", "    # 计算冷度日 (CDD) 和热度日 (HDD)\n", "    result_df['CDD'] = result_df[temp_col].apply(lambda x: max(0, x - base_temp_cooling))\n", "    result_df['HDD'] = result_df[temp_col].apply(lambda x: max(0, base_temp_heating - x))\n", "    \n", "    # 3. 温度变化率\n", "    if 'timestamp' in result_df.columns:\n", "        # 按区域和时间排序\n", "        if 'region' in result_df.columns:\n", "            result_df = result_df.sort_values(['region', 'timestamp'])\n", "            \n", "            # 计算每个区域内的温度变化\n", "            result_df['temp_change'] = result_df.groupby('region')[temp_col].diff()\n", "        else:\n", "            result_df = result_df.sort_values('timestamp')\n", "            result_df['temp_change'] = result_df[temp_col].diff()\n", "    \n", "    # 4. 温度极值特征\n", "    # 计算每天的最高温、最低温、温差\n", "    if 'timestamp' in result_df.columns and 'region' in result_df.columns:\n", "        # 提取日期部分\n", "        result_df['date'] = result_df['timestamp'].dt.date\n", "        \n", "        # 按区域和日期分组计算\n", "        daily_temp = result_df.groupby(['region', 'date'])[temp_col].agg(['min', 'max'])\n", "        daily_temp['temp_range'] = daily_temp['max'] - daily_temp['min']\n", "        \n", "        # 重置索引以便合并\n", "        daily_temp = daily_temp.reset_index()\n", "        \n", "        # 合并回原始数据\n", "        result_df = pd.merge(\n", "            result_df,\n", "            daily_temp,\n", "            on=['region', 'date'],\n", "            how='left',\n", "            suffixes=('', '_daily')\n", "        )\n", "    \n", "    # 5. 温度与负荷的交互特征\n", "    result_df['temp_load_interaction'] = result_df[temp_col] * result_df[load_col]\n", "    \n", "    return result_df\n", "\n", "# 应用温度敏感度特征提取\n", "if 'df_with_time_features' in locals() and not df_with_time_features.empty:\n", "    df_with_temp_features = create_temperature_sensitivity_features(df_with_time_features)\n", "    \n", "    # 显示新增的温度特征列\n", "    new_temp_cols = [col for col in df_with_temp_features.columns if col not in df_with_time_features.columns]\n", "    print(f\"已创建温度敏感度特征，新增列: {new_temp_cols}\")\n", "    \n", "    if new_temp_cols:\n", "        # 显示温度特征的基本统计信息\n", "        display(df_with_temp_features[new_temp_cols].describe())\n", "else:\n", "    print(\"没有合适的数据进行温度敏感度特征提取\")\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 4. 负荷滞后特征\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 创建负荷滞后特征\n", "def create_load_lag_features(df, load_col='load_value'):\n", "    \"\"\"\n", "    创建负荷滞后特征\n", "    \n", "    Args:\n", "        df: 包含负荷数据的DataFrame\n", "        load_col: 负荷列名\n", "        \n", "    Returns:\n", "        添加了负荷滞后特征的DataFrame\n", "    \"\"\"\n", "    if df is None or df.empty:\n", "        print(\"没有数据可处理\")\n", "        return df\n", "    \n", "    if load_col not in df.columns:\n", "        print(f\"负荷列 {load_col} 不存在\")\n", "        return df\n", "    \n", "    if 'timestamp' not in df.columns:\n", "        print(\"时间戳列不存在\")\n", "        return df\n", "    \n", "    # 创建副本避免修改原始数据\n", "    result_df = df.copy()\n", "    \n", "    # 确保负荷列是数值类型\n", "    try:\n", "        result_df[load_col] = pd.to_numeric(result_df[load_col], errors='coerce')\n", "    except Exception as e:\n", "        print(f\"转换数值类型时出错: {e}\")\n", "        return df\n", "    \n", "    # 按区域和时间排序\n", "    if 'region' in result_df.columns:\n", "        result_df = result_df.sort_values(['region', 'timestamp'])\n", "        \n", "        # 创建滞后特征\n", "        # 1小时前负荷\n", "        result_df['load_lag_1h'] = result_df.groupby('region')[load_col].shift(1)\n", "        \n", "        # 2小时前负荷\n", "        result_df['load_lag_2h'] = result_df.groupby('region')[load_col].shift(2)\n", "        \n", "        # 3小时前负荷\n", "        result_df['load_lag_3h'] = result_df.groupby('region')[load_col].shift(3)\n", "        \n", "        # 24小时前负荷（昨天同一时刻）\n", "        result_df['load_lag_24h'] = result_df.groupby('region')[load_col].shift(24)\n", "        \n", "        # 48小时前负荷（前天同一时刻）\n", "        result_df['load_lag_48h'] = result_df.groupby('region')[load_col].shift(48)\n", "        \n", "        # 168小时前负荷（上周同一时刻）\n", "        result_df['load_lag_168h'] = result_df.groupby('region')[load_col].shift(168)\n", "        \n", "        # 创建滑动窗口特征\n", "        # 过去24小时平均负荷\n", "        result_df['load_mean_24h'] = result_df.groupby('region')[load_col].transform(\n", "            lambda x: x.rolling(window=24, min_periods=1).mean()\n", "        )\n", "        \n", "        # 过去24小时最大负荷\n", "        result_df['load_max_24h'] = result_df.groupby('region')[load_col].transform(\n", "            lambda x: x.rolling(window=24, min_periods=1).max()\n", "        )\n", "        \n", "        # 过去24小时最小负荷\n", "        result_df['load_min_24h'] = result_df.groupby('region')[load_col].transform(\n", "            lambda x: x.rolling(window=24, min_periods=1).min()\n", "        )\n", "        \n", "        # 过去7天同一时刻的平均负荷\n", "        result_df['load_mean_same_hour_7d'] = result_df.groupby(['region', result_df['hour']])[load_col].transform(\n", "            lambda x: x.shift(1).rolling(window=7, min_periods=1).mean()\n", "        )\n", "    else:\n", "        # 如果没有区域列，则按时间排序\n", "        result_df = result_df.sort_values('timestamp')\n", "        \n", "        # 创建滞后特征\n", "        result_df['load_lag_1h'] = result_df[load_col].shift(1)\n", "        result_df['load_lag_2h'] = result_df[load_col].shift(2)\n", "        result_df['load_lag_3h'] = result_df[load_col].shift(3)\n", "        result_df['load_lag_24h'] = result_df[load_col].shift(24)\n", "        result_df['load_lag_48h'] = result_df[load_col].shift(48)\n", "        result_df['load_lag_168h'] = result_df[load_col].shift(168)\n", "        \n", "        # 创建滑动窗口特征\n", "        result_df['load_mean_24h'] = result_df[load_col].rolling(window=24, min_periods=1).mean()\n", "        result_df['load_max_24h'] = result_df[load_col].rolling(window=24, min_periods=1).max()\n", "        result_df['load_min_24h'] = result_df[load_col].rolling(window=24, min_periods=1).min()\n", "        \n", "        if 'hour' in result_df.columns:\n", "            result_df['load_mean_same_hour_7d'] = result_df.groupby(result_df['hour'])[load_col].transform(\n", "                lambda x: x.shift(1).rolling(window=7, min_periods=1).mean()\n", "            )\n", "    \n", "    # 创建负荷变化率特征\n", "    result_df['load_change_1h'] = result_df[load_col] - result_df['load_lag_1h']\n", "    result_df['load_change_24h'] = result_df[load_col] - result_df['load_lag_24h']\n", "    \n", "    # 创建负荷变化百分比特征\n", "    result_df['load_pct_change_1h'] = result_df[load_col].pct_change()\n", "    \n", "    return result_df\n", "\n", "# 应用负荷滞后特征提取\n", "if 'df_with_temp_features' in locals() and not df_with_temp_features.empty:\n", "    # 确定负荷列名\n", "    load_col = None\n", "    for col in df_with_temp_features.columns:\n", "        if 'load_value' in col.lower() or 'load' in col.lower():\n", "            load_col = col\n", "            break\n", "    \n", "    if load_col:\n", "        print(f\"使用负荷列: {load_col}\")\n", "        df_with_lag_features = create_load_lag_features(df_with_temp_features, load_col)\n", "        \n", "        # 显示新增的滞后特征列\n", "        new_lag_cols = [col for col in df_with_lag_features.columns if col not in df_with_temp_features.columns]\n", "        print(f\"已创建负荷滞后特征，新增列: {new_lag_cols}\")\n", "        \n", "        if new_lag_cols:\n", "            # 显示滞后特征的基本统计信息\n", "            display(df_with_lag_features[new_lag_cols].describe())\n", "    else:\n", "        print(\"未找到负荷列\")\n", "        df_with_lag_features = df_with_temp_features\n", "elif 'df_with_time_features' in locals() and not df_with_time_features.empty:\n", "    # 如果没有温度特征，则直接在时间特征上添加滞后特征\n", "    load_col = None\n", "    for col in df_with_time_features.columns:\n", "        if 'load_value' in col.lower() or 'load' in col.lower():\n", "            load_col = col\n", "            break\n", "    \n", "    if load_col:\n", "        print(f\"使用负荷列: {load_col}\")\n", "        df_with_lag_features = create_load_lag_features(df_with_time_features, load_col)\n", "        \n", "        # 显示新增的滞后特征列\n", "        new_lag_cols = [col for col in df_with_lag_features.columns if col not in df_with_time_features.columns]\n", "        print(f\"已创建负荷滞后特征，新增列: {new_lag_cols}\")\n", "        \n", "        if new_lag_cols:\n", "            # 显示滞后特征的基本统计信息\n", "            display(df_with_lag_features[new_lag_cols].describe())\n", "    else:\n", "        print(\"未找到负荷列\")\n", "        df_with_lag_features = df_with_time_features\n", "else:\n", "    print(\"没有合适的数据进行负荷滞后特征提取\")\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 5. 空调负荷识别特征\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 创建空调负荷识别特征\n", "def create_ac_load_features(df, load_col='load_value'):\n", "    \"\"\"\n", "    创建空调负荷识别特征\n", "    \n", "    Args:\n", "        df: 包含负荷和温度数据的DataFrame\n", "        load_col: 负荷列名\n", "        \n", "    Returns:\n", "        添加了空调负荷识别特征的DataFrame\n", "    \"\"\"\n", "    if df is None or df.empty:\n", "        print(\"没有数据可处理\")\n", "        return df\n", "    \n", "    # 确定温度列名\n", "    temp_col = None\n", "    for col in df.columns:\n", "        if 'temperature' in col.lower() or 'temp' in col.lower():\n", "            temp_col = col\n", "            break\n", "    \n", "    if temp_col is None:\n", "        print(\"未找到温度列\")\n", "        return df\n", "    \n", "    if load_col not in df.columns:\n", "        print(f\"负荷列 {load_col} 不存在\")\n", "        return df\n", "    \n", "    # 创建副本避免修改原始数据\n", "    result_df = df.copy()\n", "    \n", "    # 确保温度和负荷列是数值类型\n", "    try:\n", "        result_df[temp_col] = pd.to_numeric(result_df[temp_col], errors='coerce')\n", "        result_df[load_col] = pd.to_numeric(result_df[load_col], errors='coerce')\n", "    except Exception as e:\n", "        print(f\"转换数值类型时出错: {e}\")\n", "        return df\n", "    \n", "    # 1. 温度负荷相关性特征\n", "    if 'region' in result_df.columns:\n", "        # 按区域和日期分组\n", "        if 'date' not in result_df.columns and 'timestamp' in result_df.columns:\n", "            result_df['date'] = result_df['timestamp'].dt.date\n", "        \n", "        # 计算每个区域每天的温度和负荷相关性\n", "        corr_data = []\n", "        \n", "        for (region, date), group in result_df.groupby(['region', 'date']):\n", "            if len(group) > 5:  # 需要足够的样本计算相关性\n", "                corr = group[temp_col].corr(group[load_col])\n", "                corr_data.append({\n", "                    'region': region,\n", "                    'date': date,\n", "                    'temp_load_corr': corr\n", "                })\n", "        \n", "        if corr_data:\n", "            corr_df = pd.DataFrame(corr_data)\n", "            \n", "            # 合并回原始数据\n", "            result_df = pd.merge(\n", "                result_df,\n", "                corr_df,\n", "                on=['region', 'date'],\n", "                how='left'\n", "            )\n", "    \n", "    # 2. 空调负荷指标\n", "    # 设置温度阈值\n", "    cooling_threshold = 26  # 制冷阈值\n", "    heating_threshold = 18  # 制热阈值\n", "    \n", "    # 创建空调负荷指标\n", "    # 制冷负荷指标 - 温度高于阈值时，负荷与温度的关系\n", "    result_df['cooling_load_index'] = np.where(\n", "        result_df[temp_col] > cooling_threshold,\n", "        result_df[load_col] * (result_df[temp_col] - cooling_threshold) / result_df[temp_col],\n", "        0\n", "    )\n", "    \n", "    # 制热负荷指标 - 温度低于阈值时，负荷与温度的关系\n", "    result_df['heating_load_index'] = np.where(\n", "        result_df[temp_col] < heating_threshold,\n", "        result_df[load_col] * (heating_threshold - result_df[temp_col]) / heating_threshold,\n", "        0\n", "    )\n", "    \n", "    # 3. 负荷温度敏感度\n", "    # 如果有滞后负荷特征，计算负荷变化与温度变化的比率\n", "    if 'load_change_1h' in result_df.columns and 'temp_change' in result_df.columns:\n", "        # 避免除以零\n", "        result_df['load_temp_sensitivity'] = np.where(\n", "            result_df['temp_change'].abs() > 0.1,  # 温度变化足够大时计算\n", "            result_df['load_change_1h'] / result_df['temp_change'],\n", "            np.nan\n", "        )\n", "    \n", "    # 4. 空调使用时段标志\n", "    # 夏季白天高温时段\n", "    result_df['summer_daytime_flag'] = np.where(\n", "        (result_df['season'] == 2) & (result_df['day_period'].isin([1, 2])) & (result_df[temp_col] > cooling_threshold),\n", "        1, 0\n", "    )\n", "    \n", "    # 冬季夜间低温时段\n", "    result_df['winter_night_flag'] = np.where(\n", "        (result_df['season'] == 4) & (result_df['day_period'].isin([0, 3])) & (result_df[temp_col] < heating_threshold),\n", "        1, 0\n", "    )\n", "    \n", "    # 5. 温度极值对应的负荷特征\n", "    # 日最高温度时的负荷\n", "    if 'max' in result_df.columns:\n", "        result_df['load_at_max_temp'] = np.where(\n", "            result_df[temp_col] == result_df['max'],\n", "            result_df[load_col],\n", "            np.nan\n", "        )\n", "    \n", "    # 日最低温度时的负荷\n", "    if 'min' in result_df.columns:\n", "        result_df['load_at_min_temp'] = np.where(\n", "            result_df[temp_col] == result_df['min'],\n", "            result_df[load_col],\n", "            np.nan\n", "        )\n", "    \n", "    return result_df\n", "\n", "# 应用空调负荷识别特征提取\n", "if 'df_with_lag_features' in locals() and not df_with_lag_features.empty:\n", "    # 确定负荷列名\n", "    load_col = None\n", "    for col in df_with_lag_features.columns:\n", "        if 'load_value' in col.lower() or 'load' in col.lower():\n", "            load_col = col\n", "            break\n", "    \n", "    if load_col:\n", "        print(f\"使用负荷列: {load_col}\")\n", "        df_with_ac_features = create_ac_load_features(df_with_lag_features, load_col)\n", "        \n", "        # 显示新增的空调负荷特征列\n", "        new_ac_cols = [col for col in df_with_ac_features.columns if col not in df_with_lag_features.columns]\n", "        print(f\"已创建空调负荷识别特征，新增列: {new_ac_cols}\")\n", "        \n", "        if new_ac_cols:\n", "            # 显示空调负荷特征的基本统计信息\n", "            display(df_with_ac_features[new_ac_cols].describe())\n", "    else:\n", "        print(\"未找到负荷列\")\n", "        df_with_ac_features = df_with_lag_features\n", "else:\n", "    print(\"没有合适的数据进行空调负荷识别特征提取\")\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 6. 特征选择与保存\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 特征选择与保存\n", "def select_and_save_features(df, target_col='load_value'):\n", "    \"\"\"\n", "    特征选择与保存\n", "    \n", "    Args:\n", "        df: 包含所有特征的DataFrame\n", "        target_col: 目标列名\n", "        \n", "    Returns:\n", "        选择后的特征DataFrame\n", "    \"\"\"\n", "    if df is None or df.empty:\n", "        print(\"没有数据可处理\")\n", "        return df\n", "    \n", "    if target_col not in df.columns:\n", "        print(f\"目标列 {target_col} 不存在\")\n", "        return df\n", "    \n", "    # 创建副本避免修改原始数据\n", "    result_df = df.copy()\n", "    \n", "    # 1. 删除缺失值过多的列\n", "    missing_threshold = 0.3  # 缺失值比例阈值\n", "    missing_cols = []\n", "    \n", "    for col in result_df.columns:\n", "        missing_ratio = result_df[col].isnull().mean()\n", "        if missing_ratio > missing_threshold:\n", "            missing_cols.append(col)\n", "    \n", "    if missing_cols:\n", "        print(f\"删除缺失值过多的列: {missing_cols}\")\n", "        result_df = result_df.drop(columns=missing_cols)\n", "    \n", "    # 2. 删除常量和准常量列\n", "    constant_threshold = 0.99  # 常量值比例阈值\n", "    constant_cols = []\n", "    \n", "    for col in result_df.columns:\n", "        if result_df[col].dtype != 'object':  # 只检查数值列\n", "            value_ratio = result_df[col].value_counts(normalize=True, dropna=False).max()\n", "            if value_ratio > constant_threshold:\n", "                constant_cols.append(col)\n", "    \n", "    if constant_cols:\n", "        print(f\"删除常量和准常量列: {constant_cols}\")\n", "        result_df = result_df.drop(columns=constant_cols)\n", "    \n", "    # 3. 计算与目标变量的相关性\n", "    # 只选择数值列\n", "    numeric_cols = result_df.select_dtypes(include=['number']).columns.tolist()\n", "    \n", "    if target_col in numeric_cols:\n", "        numeric_cols.remove(target_col)\n", "    \n", "    if numeric_cols:\n", "        correlations = {}\n", "        for col in numeric_cols:\n", "            corr = result_df[col].corr(result_df[target_col])\n", "            correlations[col] = abs(corr)\n", "        \n", "        # 按相关性排序\n", "        sorted_correlations = sorted(correlations.items(), key=lambda x: x[1], reverse=True)\n", "        print(\"\\n与目标变量相关性最高的10个特征:\")\n", "        for col, corr in sorted_correlations[:10]:\n", "            print(f\"  {col}: {corr:.4f}\")\n", "    \n", "    # 4. 保存处理后的数据\n", "    output_file = PROCESSED_DATA_DIR / \"features_engineered\"\n", "    \n", "    try:\n", "        # 保存为CSV\n", "        result_df.to_csv(output_file.with_suffix('.csv'), index=False, encoding='utf-8')\n", "        print(f\"已保存CSV: {output_file.with_suffix('.csv')}\")\n", "        \n", "        # 保存为Parquet\n", "        result_df.to_parquet(output_file.with_suffix('.parquet'), index=False)\n", "        print(f\"已保存Parquet: {output_file.with_suffix('.parquet')}\")\n", "    except Exception as e:\n", "        print(f\"保存数据时出错: {e}\")\n", "    \n", "    return result_df\n", "\n", "# 应用特征选择与保存\n", "if 'df_with_ac_features' in locals() and not df_with_ac_features.empty:\n", "    # 确定目标列名\n", "    target_col = None\n", "    for col in df_with_ac_features.columns:\n", "        if 'load_value' in col.lower() or 'load' in col.lower():\n", "            target_col = col\n", "            break\n", "    \n", "    if target_col:\n", "        print(f\"使用目标列: {target_col}\")\n", "        final_df = select_and_save_features(df_with_ac_features, target_col)\n", "        \n", "        # 显示最终数据集信息\n", "        print(f\"\\n最终数据集维度: {final_df.shape}\")\n", "        print(f\"列名: {final_df.columns.tolist()}\")\n", "    else:\n", "        print(\"未找到目标列\")\n", "elif 'df_with_lag_features' in locals() and not df_with_lag_features.empty:\n", "    # 如果没有空调负荷特征，则直接使用滞后特征\n", "    target_col = None\n", "    for col in df_with_lag_features.columns:\n", "        if 'load_value' in col.lower() or 'load' in col.lower():\n", "            target_col = col\n", "            break\n", "    \n", "    if target_col:\n", "        print(f\"使用目标列: {target_col}\")\n", "        final_df = select_and_save_features(df_with_lag_features, target_col)\n", "        \n", "        # 显示最终数据集信息\n", "        print(f\"\\n最终数据集维度: {final_df.shape}\")\n", "        print(f\"列名: {final_df.columns.tolist()}\")\n", "    else:\n", "        print(\"未找到目标列\")\n", "else:\n", "    print(\"没有合适的数据进行特征选择与保存\")\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 7. 后续步骤\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "完成特征工程后，后续步骤包括：\n", "\n", "1. **模型构建**\n", "   - 创建模型构建notebook\n", "   - 数据集划分（训练集、验证集、测试集）\n", "   - 空调负荷识别模型\n", "     - 基于温度敏感度特征\n", "     - 基于时间特征\n", "     - 基于负荷特征\n", "   - 柔性调控能力评估模型\n", "     - 基于空调负荷比例\n", "     - 基于温度响应特性\n", "     - 基于历史调控效果\n", "   - 模型验证与调优\n", "\n", "2. **结果可视化与报告**\n", "   - 创建可视化与报告notebook\n", "   - 空调负荷时空分布可视化\n", "   - 柔性调控能力评估报告\n", "   - 决策支持仪表板\n", "\"\"\"\n"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}