{"cells": [{"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["# 数据加载与探索\n", "\n", "本notebook用于加载负荷数据和天气数据，并进行基本的数据探索和可视化分析。\n", "\n", "## 目标\n", "1. 加载负荷数据\n", "2. 加载天气数据\n", "3. 探索数据结构和基本统计特征\n", "4. 分析数据质量问题（缺失值、异常值等）\n", "5. 可视化数据分布和关键特征\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#!/usr/bin/env python\n", "# coding: utf-8\n", "\n", "# 导入必要的库\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import os\n", "import glob\n", "from pathlib import Path\n", "import logging\n", "from typing import Dict, List, Optional, Tuple\n", "import warnings\n", "import re\n", "\n", "# 设置中文字体支持\n", "plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签\n", "plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号\n", "\n", "# 设置显示选项\n", "pd.set_option('display.max_columns', None)\n", "pd.set_option('display.max_rows', 100)\n", "pd.set_option('display.width', 1000)\n", "\n", "# 忽略警告\n", "warnings.filterwarnings('ignore')\n", "\n", "# 配置日志\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger(__name__)\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 1. 定义数据加载类\n", "\n", "我们首先定义一个数据加载类，用于加载负荷数据和天气数据。\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class DataLoader:\n", "    \"\"\"数据加载器类\"\"\"\n", "    \n", "    def __init__(self, data_root: str = \".\"):\n", "        \"\"\"\n", "        初始化数据加载器\n", "        \n", "        Args:\n", "            data_root: 数据根目录\n", "        \"\"\"\n", "        self.data_root = Path(data_root)\n", "        self.weather_data_path = self.data_root / \"天气数据\"\n", "        self.load_data_file = self.data_root / \"complete_load_analysis_data.csv\"\n", "        \n", "        # 创建必要的目录\n", "        (self.data_root / \"data\" / \"raw\").mkdir(parents=True, exist_ok=True)\n", "        (self.data_root / \"data\" / \"processed\").mkdir(parents=True, exist_ok=True)\n", "        (self.data_root / \"data\" / \"interim\").mkdir(parents=True, exist_ok=True)\n", "    \n", "    def load_load_data(self, chunk_size: Optional[int] = None) -> pd.DataFrame:\n", "        \"\"\"\n", "        加载负荷数据\n", "        \n", "        Args:\n", "            chunk_size: 分块读取大小，None表示一次性读取\n", "            \n", "        Returns:\n", "            负荷数据DataFrame\n", "        \"\"\"\n", "        logger.info(\"开始加载负荷数据...\")\n", "        \n", "        if not self.load_data_file.exists():\n", "            raise FileNotFoundError(f\"负荷数据文件不存在: {self.load_data_file}\")\n", "        \n", "        try:\n", "            if chunk_size:\n", "                # 分块读取大文件\n", "                chunks = []\n", "                for chunk in pd.read_csv(self.load_data_file, chunksize=chunk_size):\n", "                    chunks.append(chunk)\n", "                df = pd.concat(chunks, ignore_index=True)\n", "            else:\n", "                df = pd.read_csv(self.load_data_file)\n", "            \n", "            logger.info(f\"负荷数据加载完成，形状: {df.shape}\")\n", "            logger.info(f\"列名: {list(df.columns)}\")\n", "            \n", "            return df\n", "            \n", "        except Exception as e:\n", "            logger.error(f\"加载负荷数据失败: {e}\")\n", "            raise\n", "    \n", "    def load_weather_data(self, regions: Optional[List[str]] = None, \n", "                         years: Optional[List[int]] = None) -> Dict[str, pd.DataFrame]:\n", "        \"\"\"\n", "        加载天气数据\n", "        \n", "        Args:\n", "            regions: 指定区域列表，None表示加载所有区域\n", "            years: 指定年份列表，None表示加载所有年份\n", "            \n", "        Returns:\n", "            天气数据字典，键为区域名，值为DataFrame\n", "        \"\"\"\n", "        logger.info(\"开始加载天气数据...\")\n", "        \n", "        if not self.weather_data_path.exists():\n", "            raise FileNotFoundError(f\"天气数据目录不存在: {self.weather_data_path}\")\n", "        \n", "        weather_data = {}\n", "        \n", "        # 获取所有天气数据文件\n", "        weather_files = glob.glob(str(self.weather_data_path / \"*.xls\"))\n", "        weather_files.extend(glob.glob(str(self.weather_data_path / \"*.xlsx\")))\n", "        \n", "        logger.info(f\"找到 {len(weather_files)} 个天气数据文件\")\n", "        \n", "        for file_path in weather_files:\n", "            file_name = Path(file_path).stem\n", "            \n", "            # 解析文件名获取区域和年份\n", "            if \"城市24小时\" in file_name:\n", "                # 处理城市24小时数据文件\n", "                continue  # 暂时跳过，后续单独处理\n", "            \n", "            # 解析区域名和年份\n", "            parts = file_name.split()\n", "            if len(parts) >= 2:\n", "                region = parts[0]\n", "                year = int(parts[1])\n", "                \n", "                # 过滤条件\n", "                if regions and region not in regions:\n", "                    continue\n", "                if years and year not in years:\n", "                    continue\n", "                \n", "                try:\n", "                    # 读取Excel文件\n", "                    df = pd.read_excel(file_path)\n", "                    \n", "                    # 标准化列名\n", "                    df.columns = [col.strip() for col in df.columns]\n", "                    \n", "                    # 添加区域和年份信息\n", "                    df['region'] = region\n", "                    df['year'] = year\n", "                    \n", "                    # 存储数据\n", "                    key = f\"{region}_{year}\"\n", "                    weather_data[key] = df\n", "                    \n", "                    logger.info(f\"加载 {key}: {df.shape}\")\n", "                    \n", "                except Exception as e:\n", "                    logger.warning(f\"加载文件 {file_path} 失败: {e}\")\n", "                    continue\n", "        \n", "        logger.info(f\"天气数据加载完成，共加载 {len(weather_data)} 个文件\")\n", "        return weather_data\n", "    \n", "    def load_city_24h_data(self) -> pd.DataFrame:\n", "        \"\"\"\n", "        加载城市24小时数据\n", "        \n", "        Returns:\n", "            城市24小时数据DataFrame\n", "        \"\"\"\n", "        logger.info(\"开始加载城市24小时数据...\")\n", "        \n", "        city_24h_files = []\n", "        for pattern in [\"城市24小时-2023.xlsx\", \"城市24小时-24-25.xlsx\"]:\n", "            file_path = self.weather_data_path / pattern\n", "            if file_path.exists():\n", "                city_24h_files.append(file_path)\n", "        \n", "        if not city_24h_files:\n", "            logger.warning(\"未找到城市24小时数据文件\")\n", "            return pd.DataFrame()\n", "        \n", "        all_data = []\n", "        for file_path in city_24h_files:\n", "            try:\n", "                df = pd.read_excel(file_path)\n", "                df.columns = [col.strip() for col in df.columns]\n", "                all_data.append(df)\n", "                logger.info(f\"加载 {file_path.name}: {df.shape}\")\n", "            except Exception as e:\n", "                logger.warning(f\"加载文件 {file_path} 失败: {e}\")\n", "                continue\n", "        \n", "        if all_data:\n", "            result = pd.concat(all_data, ignore_index=True)\n", "            logger.info(f\"城市24小时数据加载完成，总形状: {result.shape}\")\n", "            return result\n", "        else:\n", "            return pd.DataFrame()\n", "    \n", "    def preprocess_load_data(self, df: pd.DataFrame) -> pd.DataFrame:\n", "        \"\"\"\n", "        预处理负荷数据\n", "        \n", "        Args:\n", "            df: 原始负荷数据\n", "            \n", "        Returns:\n", "            预处理后的负荷数据\n", "        \"\"\"\n", "        logger.info(\"开始预处理负荷数据...\")\n", "        \n", "        # 复制数据避免修改原始数据\n", "        df_processed = df.copy()\n", "        \n", "        # 处理时间列\n", "        time_columns = [col for col in df_processed.columns if '时间' in col or 'date' in col.lower() or 'time' in col.lower()]\n", "        if time_columns:\n", "            time_col = time_columns[0]\n", "            df_processed[time_col] = pd.to_datetime(df_processed[time_col], errors='coerce')\n", "            df_processed = df_processed.dropna(subset=[time_col])\n", "        \n", "        # 处理数值列\n", "        numeric_columns = df_processed.select_dtypes(include=[np.number]).columns\n", "        for col in numeric_columns:\n", "            # 处理异常值\n", "            Q1 = df_processed[col].quantile(0.25)\n", "            Q3 = df_processed[col].quantile(0.75)\n", "            IQR = Q3 - Q1\n", "            lower_bound = Q1 - 1.5 * IQR\n", "            upper_bound = Q3 + 1.5 * IQR\n", "            \n", "            # 将异常值设为NaN\n", "            df_processed.loc[(df_processed[col] < lower_bound) | (df_processed[col] > upper_bound), col] = np.nan\n", "        \n", "        # 填充缺失值\n", "        df_processed = df_processed.fillna(method='ffill').fillna(method='bfill')\n", "        \n", "        logger.info(f\"负荷数据预处理完成，最终形状: {df_processed.shape}\")\n", "        return df_processed\n", "    \n", "    def preprocess_weather_data(self, weather_data: Dict[str, pd.DataFrame]) -> pd.DataFrame:\n", "        \"\"\"\n", "        预处理天气数据\n", "        \n", "        Args:\n", "            weather_data: 原始天气数据字典\n", "            \n", "        Returns:\n", "            预处理后的天气数据DataFrame\n", "        \"\"\"\n", "        logger.info(\"开始预处理天气数据...\")\n", "        \n", "        all_weather_data = []\n", "        \n", "        for key, df in weather_data.items():\n", "            df_processed = df.copy()\n", "            \n", "            # 标准化时间列\n", "            time_columns = [col for col in df_processed.columns if '时间' in col or 'date' in col.lower() or 'time' in col.lower()]\n", "            if time_columns:\n", "                time_col = time_columns[0]\n", "                df_processed[time_col] = pd.to_datetime(df_processed[time_col], errors='coerce')\n", "                df_processed = df_processed.dropna(subset=[time_col])\n", "            \n", "            # 处理温度列\n", "            temp_columns = [col for col in df_processed.columns if '温度' in col or 'temp' in col.lower()]\n", "            if temp_columns:\n", "                temp_col = temp_columns[0]\n", "                df_processed[temp_col] = pd.to_numeric(df_processed[temp_col], errors='coerce')\n", "            \n", "            all_weather_data.append(df_processed)\n", "        \n", "        if all_weather_data:\n", "            result = pd.concat(all_weather_data, ignore_index=True)\n", "            logger.info(f\"天气数据预处理完成，总形状: {result.shape}\")\n", "            return result\n", "        else:\n", "            return pd.DataFrame()\n", "    \n", "    def save_processed_data(self, df: pd.DataFrame, filename: str, \n", "                          format: str = 'parquet') -> None:\n", "        \"\"\"\n", "        保存处理后的数据\n", "        \n", "        Args:\n", "            df: 处理后的DataFrame\n", "            filename: 输出文件名\n", "            format: 输出格式，支持'csv'和'parquet'\n", "        \"\"\"\n", "        output_dir = self.data_root / \"data\" / \"processed\"\n", "        output_dir.mkdir(parents=True, exist_ok=True)\n", "        \n", "        if format.lower() == 'csv':\n", "            output_path = output_dir / f\"{filename}.csv\"\n", "            df.to_csv(output_path, index=False)\n", "        elif format.lower() == 'parquet':\n", "            output_path = output_dir / f\"{filename}.parquet\"\n", "            df.to_parquet(output_path, index=False)\n", "        else:\n", "            raise ValueError(f\"不支持的输出格式: {format}\")\n", "        \n", "        logger.info(f\"数据已保存至: {output_path}\")\n", "\n", "# 创建数据加载器实例\n", "data_loader = DataLoader()\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 2. 加载负荷数据\n", "\n", "加载负荷数据并进行基本探索。\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 加载负荷数据（使用分块加载以处理大文件）\n", "load_df = data_loader.load_load_data(chunk_size=10000)\n", "\n", "# 显示数据基本信息\n", "print(f\"负荷数据形状: {load_df.shape}\")\n", "print(f\"负荷数据列名: {load_df.columns.tolist()}\")\n", "\n", "# 查看数据前几行\n", "load_df.head()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 查看数据类型\n", "load_df.dtypes\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 数据基本统计信息\n", "load_df.describe()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 检查缺失值\n", "missing_values = load_df.isnull().sum()\n", "missing_percent = (missing_values / len(load_df)) * 100\n", "\n", "# 创建缺失值摘要\n", "missing_summary = pd.DataFrame({\n", "    '缺失值数量': missing_values,\n", "    '缺失值百分比': missing_percent\n", "})\n", "\n", "# 只显示有缺失值的列\n", "missing_summary = missing_summary[missing_summary['缺失值数量'] > 0].sort_values('缺失值百分比', ascending=False)\n", "\n", "if len(missing_summary) > 0:\n", "    print(\"存在缺失值的列:\")\n", "    display(missing_summary)\n", "else:\n", "    print(\"数据中没有缺失值。\")\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 3. 负荷数据可视化分析\n", "\n", "对负荷数据进行可视化分析，包括分布、时序趋势等。\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 确保时间列已正确转换为datetime类型\n", "time_columns = [col for col in load_df.columns if '时间' in col or 'date' in col.lower() or 'time' in col.lower()]\n", "if time_columns:\n", "    time_col = time_columns[0]\n", "    load_df[time_col] = pd.to_datetime(load_df[time_col], errors='coerce')\n", "    \n", "    # 设置时间列为索引（用于时序分析）\n", "    load_df_time_indexed = load_df.set_index(time_col)\n", "    \n", "    # 选择一些数值列进行可视化\n", "    numeric_cols = load_df.select_dtypes(include=[np.number]).columns[:5]  # 选择前5个数值列\n", "    \n", "    plt.figure(figsize=(15, 8))\n", "    for col in numeric_cols:\n", "        plt.plot(load_df_time_indexed.index, load_df_time_indexed[col], label=col)\n", "    \n", "    plt.title('负荷数据时序趋势')\n", "    plt.xlabel('时间')\n", "    plt.ylabel('负荷值')\n", "    plt.legend()\n", "    plt.grid(True)\n", "    plt.xticks(rotation=45)\n", "    plt.tight_layout()\n", "    plt.show()\n", "else:\n", "    print(\"未找到时间列，无法进行时序分析\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 负荷数据分布可视化\n", "plt.figure(figsize=(15, 10))\n", "\n", "# 选择前4个数值列进行可视化\n", "numeric_cols = load_df.select_dtypes(include=[np.number]).columns[:4]\n", "\n", "for i, col in enumerate(numeric_cols, 1):\n", "    plt.subplot(2, 2, i)\n", "    \n", "    # 绘制直方图和密度曲线\n", "    sns.histplot(load_df[col].dropna(), kde=True)\n", "    \n", "    plt.title(f'{col} 分布')\n", "    plt.xlabel(col)\n", "    plt.ylabel('频率')\n", "    plt.grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 箱线图分析异常值\n", "plt.figure(figsize=(15, 8))\n", "\n", "# 选择前6个数值列进行可视化\n", "numeric_cols = load_df.select_dtypes(include=[np.number]).columns[:6]\n", "\n", "# 创建箱线图\n", "sns.boxplot(data=load_df[numeric_cols])\n", "\n", "plt.title('负荷数据箱线图 - 异常值分析')\n", "plt.xticks(rotation=45)\n", "plt.grid(True, alpha=0.3)\n", "plt.tight_layout()\n", "plt.show()\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 4. 加载天气数据\n", "\n", "加载天气数据并进行基本探索。\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 加载天气数据\n", "weather_data_dict = data_loader.load_weather_data()\n", "\n", "# 查看加载了哪些区域的天气数据\n", "print(f\"加载的天气数据区域: {list(weather_data_dict.keys())}\")\n", "\n", "# 选择第一个区域的数据进行查看\n", "if weather_data_dict:\n", "    first_key = list(weather_data_dict.keys())[0]\n", "    first_df = weather_data_dict[first_key]\n", "    \n", "    print(f\"\\n查看 {first_key} 的天气数据:\")\n", "    print(f\"形状: {first_df.shape}\")\n", "    print(f\"列名: {first_df.columns.tolist()}\")\n", "    \n", "    # 显示前几行\n", "    display(first_df.head())\n", "else:\n", "    print(\"未加载到任何天气数据\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 加载城市24小时数据\n", "city_24h_df = data_loader.load_city_24h_data()\n", "\n", "if not city_24h_df.empty:\n", "    print(f\"城市24小时数据形状: {city_24h_df.shape}\")\n", "    print(f\"列名: {city_24h_df.columns.tolist()}\")\n", "    \n", "    # 显示前几行\n", "    display(city_24h_df.head())\n", "else:\n", "    print(\"未加载到城市24小时数据\")\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 5. 天气数据可视化分析\n", "\n", "对天气数据进行可视化分析，包括温度、湿度等关键指标。\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 将所有区域的天气数据合并为一个DataFrame用于分析\n", "if weather_data_dict:\n", "    all_weather_df = pd.concat(weather_data_dict.values(), ignore_index=True)\n", "    \n", "    # 查找温度列\n", "    temp_columns = [col for col in all_weather_df.columns if '温度' in col or 'temp' in col.lower()]\n", "    \n", "    if temp_columns:\n", "        temp_col = temp_columns[0]\n", "        \n", "        # 确保温度列是数值类型\n", "        all_weather_df[temp_col] = pd.to_numeric(all_weather_df[temp_col], errors='coerce')\n", "        \n", "        # 按区域绘制温度箱线图\n", "        plt.figure(figsize=(15, 8))\n", "        sns.boxplot(x='region', y=temp_col, data=all_weather_df)\n", "        plt.title('不同区域温度分布')\n", "        plt.xlabel('区域')\n", "        plt.ylabel('温度 (°C)')\n", "        plt.xticks(rotation=45)\n", "        plt.grid(True, alpha=0.3)\n", "        plt.tight_layout()\n", "        plt.show()\n", "    else:\n", "        print(\"未找到温度列\")\n", "else:\n", "    print(\"没有天气数据可供分析\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 查找湿度列并可视化\n", "if weather_data_dict:\n", "    all_weather_df = pd.concat(weather_data_dict.values(), ignore_index=True)\n", "    \n", "    # 查找湿度列\n", "    humidity_columns = [col for col in all_weather_df.columns if '湿度' in col or 'humid' in col.lower()]\n", "    \n", "    if humidity_columns:\n", "        humidity_col = humidity_columns[0]\n", "        \n", "        # 确保湿度列是数值类型\n", "        all_weather_df[humidity_col] = pd.to_numeric(all_weather_df[humidity_col], errors='coerce')\n", "        \n", "        # 按区域绘制湿度箱线图\n", "        plt.figure(figsize=(15, 8))\n", "        sns.boxplot(x='region', y=humidity_col, data=all_weather_df)\n", "        plt.title('不同区域湿度分布')\n", "        plt.xlabel('区域')\n", "        plt.ylabel('湿度 (%)')\n", "        plt.xticks(rotation=45)\n", "        plt.grid(True, alpha=0.3)\n", "        plt.tight_layout()\n", "        plt.show()\n", "    else:\n", "        print(\"未找到湿度列\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 温度与时间关系可视化\n", "if weather_data_dict:\n", "    all_weather_df = pd.concat(weather_data_dict.values(), ignore_index=True)\n", "    \n", "    # 查找温度列和时间列\n", "    temp_columns = [col for col in all_weather_df.columns if '温度' in col or 'temp' in col.lower()]\n", "    time_columns = [col for col in all_weather_df.columns if '时间' in col or 'date' in col.lower() or 'time' in col.lower()]\n", "    \n", "    if temp_columns and time_columns:\n", "        temp_col = temp_columns[0]\n", "        time_col = time_columns[0]\n", "        \n", "        # 确保温度列是数值类型，时间列是datetime类型\n", "        all_weather_df[temp_col] = pd.to_numeric(all_weather_df[temp_col], errors='coerce')\n", "        all_weather_df[time_col] = pd.to_datetime(all_weather_df[time_col], errors='coerce')\n", "        \n", "        # 选择一个区域进行可视化\n", "        if 'region' in all_weather_df.columns and len(all_weather_df['region'].unique()) > 0:\n", "            selected_region = all_weather_df['region'].unique()[0]\n", "            region_df = all_weather_df[all_weather_df['region'] == selected_region].copy()\n", "            \n", "            # 按时间排序\n", "            region_df = region_df.sort_values(by=time_col)\n", "            \n", "            # 绘制温度随时间变化图\n", "            plt.figure(figsize=(15, 6))\n", "            plt.plot(region_df[time_col], region_df[temp_col], marker='o', linestyle='-', alpha=0.7)\n", "            plt.title(f'{selected_region} 温度随时间变化')\n", "            plt.xlabel('时间')\n", "            plt.ylabel('温度 (°C)')\n", "            plt.grid(True)\n", "            plt.xticks(rotation=45)\n", "            plt.tight_layout()\n", "            plt.show()\n", "        else:\n", "            print(\"无法确定区域信息\")\n", "    else:\n", "        print(\"未找到温度列或时间列\")\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 6. 数据预处理\n", "\n", "对负荷数据和天气数据进行预处理，为后续分析做准备。\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 预处理负荷数据\n", "processed_load_df = data_loader.preprocess_load_data(load_df)\n", "\n", "# 显示预处理前后的对比\n", "print(f\"预处理前负荷数据形状: {load_df.shape}\")\n", "print(f\"预处理后负荷数据形状: {processed_load_df.shape}\")\n", "\n", "# 检查预处理后的缺失值\n", "missing_after = processed_load_df.isnull().sum()\n", "missing_after_percent = (missing_after / len(processed_load_df)) * 100\n", "\n", "# 创建缺失值摘要\n", "missing_after_summary = pd.DataFrame({\n", "    '缺失值数量': missing_after,\n", "    '缺失值百分比': missing_after_percent\n", "})\n", "\n", "# 只显示有缺失值的列\n", "missing_after_summary = missing_after_summary[missing_after_summary['缺失值数量'] > 0].sort_values('缺失值百分比', ascending=False)\n", "\n", "if len(missing_after_summary) > 0:\n", "    print(\"\\n预处理后仍存在缺失值的列:\")\n", "    display(missing_after_summary)\n", "else:\n", "    print(\"\\n预处理后数据中没有缺失值。\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 预处理天气数据\n", "if weather_data_dict:\n", "    processed_weather_df = data_loader.preprocess_weather_data(weather_data_dict)\n", "    \n", "    print(f\"预处理后天气数据形状: {processed_weather_df.shape}\")\n", "    \n", "    # 检查预处理后的缺失值\n", "    weather_missing = processed_weather_df.isnull().sum()\n", "    weather_missing_percent = (weather_missing / len(processed_weather_df)) * 100\n", "    \n", "    # 创建缺失值摘要\n", "    weather_missing_summary = pd.DataFrame({\n", "        '缺失值数量': weather_missing,\n", "        '缺失值百分比': weather_missing_percent\n", "    })\n", "    \n", "    # 只显示有缺失值的列\n", "    weather_missing_summary = weather_missing_summary[weather_missing_summary['缺失值数量'] > 0].sort_values('缺失值百分比', ascending=False)\n", "    \n", "    if len(weather_missing_summary) > 0:\n", "        print(\"\\n预处理后天气数据仍存在缺失值的列:\")\n", "        display(weather_missing_summary.head(10))  # 只显示前10个有缺失的列\n", "    else:\n", "        print(\"\\n预处理后天气数据中没有缺失值。\")\n", "else:\n", "    print(\"没有天气数据可供预处理\")\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 7. 保存处理后的数据\n", "\n", "将预处理后的数据保存为后续分析使用的格式。\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 保存预处理后的负荷数据\n", "data_loader.save_processed_data(processed_load_df, \"processed_load_data\", format='parquet')\n", "\n", "# 保存预处理后的天气数据\n", "if 'processed_weather_df' in locals() and not processed_weather_df.empty:\n", "    data_loader.save_processed_data(processed_weather_df, \"processed_weather_data\", format='parquet')\n", "\n", "# 保存城市24小时数据\n", "if 'city_24h_df' in locals() and not city_24h_df.empty:\n", "    data_loader.save_processed_data(city_24h_df, \"city_24h_data\", format='parquet')\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 8. 总结\n", "\n", "在本notebook中，我们完成了以下工作：\n", "\n", "1. 加载了负荷数据，并进行了基本探索和可视化分析\n", "2. 加载了天气数据，包括区域天气数据和城市24小时数据\n", "3. 对天气数据进行了可视化分析，包括温度、湿度等关键指标\n", "4. 对负荷数据和天气数据进行了预处理，包括处理缺失值和异常值\n", "5. 将预处理后的数据保存为后续分析使用的格式\n", "\n", "下一步，我们将在 `02_数据标准化与合并.ipynb` 中对数据进行标准化处理，并将负荷数据和天气数据合并为一个大宽表，以便进行后续的特征工程和模型构建。\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["# 空调负荷柔性调控能力分析系统 - 数据加载与探索\n", "\n", "本notebook用于加载和探索负荷数据与天气数据，分析数据质量和基本统计特征。\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 导入必要的库\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import os\n", "import glob\n", "from pathlib import Path\n", "import warnings\n", "import re\n", "from datetime import datetime, timedelta\n", "\n", "# 设置中文显示\n", "plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签\n", "plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号\n", "warnings.filterwarnings('ignore')\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 定义数据路径\n", "DATA_DIR = Path(\"../data\")\n", "RAW_DATA_DIR = DATA_DIR / \"raw\"\n", "PROCESSED_DATA_DIR = DATA_DIR / \"processed\"\n", "WEATHER_DIR = Path(\"../天气数据\")\n", "\n", "# 确保目录存在\n", "PROCESSED_DATA_DIR.mkdir(parents=True, exist_ok=True)\n", "\n", "print(f\"原始数据目录: {RAW_DATA_DIR}\")\n", "print(f\"处理后数据目录: {PROCESSED_DATA_DIR}\")\n", "print(f\"天气数据目录: {WEATHER_DIR}\")\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 1. 负荷数据加载与探索\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 分块读取负荷数据\n", "def load_load_data(file_path, chunksize=100000):\n", "    \"\"\"\n", "    分块读取大型负荷数据文件\n", "    \n", "    Args:\n", "        file_path: 数据文件路径\n", "        chunksize: 每块的行数\n", "        \n", "    Returns:\n", "        完整的DataFrame\n", "    \"\"\"\n", "    try:\n", "        # 尝试读取parquet格式（如果存在）\n", "        if Path(file_path).with_suffix('.parquet').exists():\n", "            return pd.read_parquet(Path(file_path).with_suffix('.parquet'))\n", "        \n", "        # 读取CSV\n", "        chunks = []\n", "        for chunk in pd.read_csv(file_path, chunksize=chunksize):\n", "            chunks.append(chunk)\n", "            print(f\"读取了 {len(chunks) * chunksize} 行数据...\")\n", "        \n", "        return pd.concat(chunks, ignore_index=True)\n", "    except Exception as e:\n", "        print(f\"读取数据时出错: {e}\")\n", "        return pd.DataFrame()\n", "\n", "# 加载负荷数据\n", "load_file = RAW_DATA_DIR / \"load_data.csv\"\n", "if load_file.exists():\n", "    load_df = load_load_data(load_file)\n", "    print(f\"负荷数据加载完成，共 {len(load_df)} 行\")\n", "else:\n", "    print(f\"负荷数据文件不存在: {load_file}\")\n", "    load_df = pd.DataFrame()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 探索负荷数据基本信息\n", "if not load_df.empty:\n", "    print(\"负荷数据基本信息:\")\n", "    print(f\"数据维度: {load_df.shape}\")\n", "    print(\"\\n列名:\")\n", "    print(load_df.columns.tolist())\n", "    \n", "    print(\"\\n数据类型:\")\n", "    print(load_df.dtypes)\n", "    \n", "    print(\"\\n前5行数据:\")\n", "    display(load_df.head())\n", "    \n", "    print(\"\\n数据统计摘要:\")\n", "    display(load_df.describe())\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 检查缺失值\n", "if not load_df.empty:\n", "    missing_values = load_df.isnull().sum()\n", "    missing_percent = (missing_values / len(load_df)) * 100\n", "    \n", "    missing_df = pd.DataFrame({\n", "        '缺失值数量': missing_values,\n", "        '缺失百分比': missing_percent\n", "    })\n", "    \n", "    print(\"缺失值情况:\")\n", "    display(missing_df[missing_df['缺失值数量'] > 0])\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 2. 天气数据探索与分析\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 获取所有天气数据文件\n", "def get_weather_files():\n", "    \"\"\"获取所有天气数据文件\"\"\"\n", "    # 获取区域天气数据文件\n", "    regional_files = []\n", "    for ext in ['*.xls', '*.xlsx']:\n", "        pattern = str(WEATHER_DIR / ext)\n", "        regional_files.extend(glob.glob(pattern))\n", "    \n", "    # 获取城市24小时数据文件\n", "    city_24h_files = []\n", "    for file in regional_files:\n", "        if '城市24小时' in file:\n", "            city_24h_files.append(file)\n", "            regional_files.remove(file)\n", "    \n", "    return regional_files, city_24h_files\n", "\n", "# 获取文件列表\n", "regional_files, city_24h_files = get_weather_files()\n", "\n", "print(f\"区域天气数据文件数量: {len(regional_files)}\")\n", "print(f\"城市24小时数据文件数量: {len(city_24h_files)}\")\n", "\n", "# 显示部分文件名\n", "print(\"\\n区域天气数据文件示例:\")\n", "for file in regional_files[:5]:\n", "    print(f\"- {os.path.basename(file)}\")\n", "\n", "print(\"\\n城市24小时数据文件:\")\n", "for file in city_24h_files:\n", "    print(f\"- {os.path.basename(file)}\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 分析区域天气数据文件结构\n", "def analyze_regional_file(file_path):\n", "    \"\"\"分析区域天气数据文件结构\"\"\"\n", "    try:\n", "        # 尝试读取Excel文件的所有sheet\n", "        xls = pd.ExcelFile(file_path)\n", "        sheet_names = xls.sheet_names\n", "        \n", "        # 获取文件名信息\n", "        file_name = os.path.basename(file_path)\n", "        region_match = re.search(r'(.+?)(?:23|24|25)\\.xls', file_name)\n", "        year_match = re.search(r'(\\d{2})\\.xls', file_name)\n", "        \n", "        region = region_match.group(1) if region_match else \"未知区域\"\n", "        year = f\"20{year_match.group(1)}\" if year_match else \"未知年份\"\n", "        \n", "        # 读取第一个sheet的数据\n", "        df = pd.read_excel(file_path, sheet_name=0)\n", "        \n", "        return {\n", "            'file_name': file_name,\n", "            'region': region,\n", "            'year': year,\n", "            'sheets': sheet_names,\n", "            'columns': df.columns.tolist(),\n", "            'rows': len(df),\n", "            'sample': df.head(2)\n", "        }\n", "    except Exception as e:\n", "        print(f\"分析文件 {file_path} 时出错: {e}\")\n", "        return None\n", "\n", "# 分析几个区域天气数据文件\n", "sample_files = regional_files[:3]\n", "for file in sample_files:\n", "    result = analyze_regional_file(file)\n", "    if result:\n", "        print(f\"\\n文件名: {result['file_name']}\")\n", "        print(f\"区域: {result['region']}\")\n", "        print(f\"年份: {result['year']}\")\n", "        print(f\"工作表: {result['sheets']}\")\n", "        print(f\"列数: {len(result['columns'])}\")\n", "        print(f\"行数: {result['rows']}\")\n", "        print(\"列名:\")\n", "        print(result['columns'])\n", "        print(\"数据样例:\")\n", "        display(result['sample'])\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 分析城市24小时数据文件结构\n", "def analyze_city_24h_file(file_path):\n", "    \"\"\"分析城市24小时数据文件结构\"\"\"\n", "    try:\n", "        # 尝试读取Excel文件的所有sheet\n", "        xls = pd.ExcelFile(file_path)\n", "        sheet_names = xls.sheet_names\n", "        \n", "        # 获取文件名信息\n", "        file_name = os.path.basename(file_path)\n", "        year_match = re.search(r'-(\\d{4})', file_name)\n", "        years_match = re.search(r'-(\\d{2})-(\\d{2})', file_name)\n", "        \n", "        if year_match:\n", "            year = year_match.group(1)\n", "        elif years_match:\n", "            year = f\"20{years_match.group(1)}-20{years_match.group(2)}\"\n", "        else:\n", "            year = \"未知年份\"\n", "        \n", "        # 读取第一个sheet的数据\n", "        df = pd.read_excel(file_path, sheet_name=0)\n", "        \n", "        return {\n", "            'file_name': file_name,\n", "            'year': year,\n", "            'sheets': sheet_names,\n", "            'columns': df.columns.tolist(),\n", "            'rows': len(df),\n", "            'sample': df.head(2)\n", "        }\n", "    except Exception as e:\n", "        print(f\"分析文件 {file_path} 时出错: {e}\")\n", "        return None\n", "\n", "# 分析城市24小时数据文件\n", "for file in city_24h_files:\n", "    result = analyze_city_24h_file(file)\n", "    if result:\n", "        print(f\"\\n文件名: {result['file_name']}\")\n", "        print(f\"年份: {result['year']}\")\n", "        print(f\"工作表: {result['sheets']}\")\n", "        print(f\"列数: {len(result['columns'])}\")\n", "        print(f\"行数: {result['rows']}\")\n", "        print(\"列名:\")\n", "        print(result['columns'])\n", "        print(\"数据样例:\")\n", "        display(result['sample'])\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 3. 数据标准化与合并\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 标准化区域天气数据\n", "def standardize_regional_weather(file_path):\n", "    \"\"\"标准化区域天气数据\"\"\"\n", "    try:\n", "        # 获取文件名信息\n", "        file_name = os.path.basename(file_path)\n", "        region_match = re.search(r'(.+?)(?:23|24|25)\\.xls', file_name)\n", "        year_match = re.search(r'(\\d{2})\\.xls', file_name)\n", "        \n", "        region = region_match.group(1) if region_match else \"未知区域\"\n", "        year = f\"20{year_match.group(1)}\" if year_match else \"未知年份\"\n", "        \n", "        # 读取数据\n", "        df = pd.read_excel(file_path, sheet_name=0)\n", "        \n", "        # 标准化列名 - 根据实际情况调整\n", "        column_mapping = {\n", "            '时间': 'time',\n", "            '气温': 'temperature',\n", "            '湿度': 'humidity',\n", "            '风速': 'wind_speed',\n", "            '风向': 'wind_direction',\n", "            '降水': 'precipitation',\n", "            '气压': 'pressure'\n", "        }\n", "        \n", "        # 应用可用的列映射\n", "        for old_col, new_col in column_mapping.items():\n", "            if old_col in df.columns:\n", "                df.rename(columns={old_col: new_col}, inplace=True)\n", "        \n", "        # 添加元数据列\n", "        df['region'] = region\n", "        df['year'] = year\n", "        df['source_file'] = file_name\n", "        \n", "        # 处理时间格式\n", "        if 'time' in df.columns:\n", "            # 尝试转换时间格式\n", "            try:\n", "                if df['time'].dtype == 'object':\n", "                    df['time'] = pd.to_datetime(df['time'], errors='coerce')\n", "            except Exception as e:\n", "                print(f\"转换时间格式时出错: {e}\")\n", "        \n", "        return df\n", "    except Exception as e:\n", "        print(f\"标准化文件 {file_path} 时出错: {e}\")\n", "        return pd.DataFrame()\n", "\n", "# 尝试标准化几个区域天气数据文件\n", "sample_files = regional_files[:3]\n", "standardized_dfs = []\n", "\n", "for file in sample_files:\n", "    print(f\"标准化文件: {os.path.basename(file)}\")\n", "    df = standardize_regional_weather(file)\n", "    if not df.empty:\n", "        standardized_dfs.append(df)\n", "        print(f\"  成功，{len(df)} 行\")\n", "    else:\n", "        print(\"  失败\")\n", "\n", "# 合并标准化后的数据\n", "if standardized_dfs:\n", "    merged_df = pd.concat(standardized_dfs, ignore_index=True)\n", "    print(f\"\\n合并后数据: {len(merged_df)} 行\")\n", "    display(merged_df.head())\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 标准化城市24小时数据\n", "def standardize_city_24h_weather(file_path):\n", "    \"\"\"标准化城市24小时数据\"\"\"\n", "    try:\n", "        # 获取文件名信息\n", "        file_name = os.path.basename(file_path)\n", "        year_match = re.search(r'-(\\d{4})', file_name)\n", "        years_match = re.search(r'-(\\d{2})-(\\d{2})', file_name)\n", "        \n", "        if year_match:\n", "            year = year_match.group(1)\n", "        elif years_match:\n", "            year = f\"20{years_match.group(1)}-20{years_match.group(2)}\"\n", "        else:\n", "            year = \"未知年份\"\n", "        \n", "        # 读取数据\n", "        df = pd.read_excel(file_path, sheet_name=0)\n", "        \n", "        # 标准化列名 - 根据实际情况调整\n", "        column_mapping = {\n", "            '日期': 'date',\n", "            '城市': 'city',\n", "            'AQI': 'aqi',\n", "            '空气质量指数类别': 'aqi_category',\n", "            'PM2.5': 'pm25',\n", "            'PM10': 'pm10',\n", "            'SO2': 'so2',\n", "            'NO2': 'no2',\n", "            'O3': 'o3',\n", "            'CO': 'co'\n", "        }\n", "        \n", "        # 应用可用的列映射\n", "        for old_col, new_col in column_mapping.items():\n", "            if old_col in df.columns:\n", "                df.rename(columns={old_col: new_col}, inplace=True)\n", "        \n", "        # 添加元数据列\n", "        df['year'] = year\n", "        df['source_file'] = file_name\n", "        \n", "        # 处理日期格式\n", "        if 'date' in df.columns:\n", "            try:\n", "                if df['date'].dtype == 'object':\n", "                    df['date'] = pd.to_datetime(df['date'], errors='coerce')\n", "            except Exception as e:\n", "                print(f\"转换日期格式时出错: {e}\")\n", "        \n", "        return df\n", "    except Exception as e:\n", "        print(f\"标准化文件 {file_path} 时出错: {e}\")\n", "        return pd.DataFrame()\n", "\n", "# 标准化城市24小时数据文件\n", "city_24h_standardized_dfs = []\n", "\n", "for file in city_24h_files:\n", "    print(f\"标准化文件: {os.path.basename(file)}\")\n", "    df = standardize_city_24h_weather(file)\n", "    if not df.empty:\n", "        city_24h_standardized_dfs.append(df)\n", "        print(f\"  成功，{len(df)} 行\")\n", "    else:\n", "        print(\"  失败\")\n", "\n", "# 合并标准化后的数据\n", "if city_24h_standardized_dfs:\n", "    city_24h_merged_df = pd.concat(city_24h_standardized_dfs, ignore_index=True)\n", "    print(f\"\\n合并后数据: {len(city_24h_merged_df)} 行\")\n", "    display(city_24h_merged_df.head())\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 保存标准化后的数据\n", "def save_standardized_data(df, name):\n", "    \"\"\"保存标准化后的数据\"\"\"\n", "    if df is None or df.empty:\n", "        print(f\"没有数据可保存: {name}\")\n", "        return\n", "    \n", "    # 创建保存路径\n", "    csv_path = PROCESSED_DATA_DIR / f\"{name}.csv\"\n", "    parquet_path = PROCESSED_DATA_DIR / f\"{name}.parquet\"\n", "    \n", "    try:\n", "        # 保存为CSV\n", "        df.to_csv(csv_path, index=False, encoding='utf-8')\n", "        print(f\"已保存CSV: {csv_path}\")\n", "        \n", "        # 保存为Parquet\n", "        df.to_parquet(parquet_path, index=False)\n", "        print(f\"已保存Parquet: {parquet_path}\")\n", "    except Exception as e:\n", "        print(f\"保存数据时出错: {e}\")\n", "\n", "# 保存示例数据\n", "if 'merged_df' in locals() and not merged_df.empty:\n", "    save_standardized_data(merged_df, \"sample_weather_regional_merged\")\n", "    \n", "if 'city_24h_merged_df' in locals() and not city_24h_merged_df.empty:\n", "    save_standardized_data(city_24h_merged_df, \"sample_weather_city_24h_merged\")\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 4. 全量天气数据处理\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 处理全部区域天气数据\n", "def process_all_regional_weather():\n", "    \"\"\"处理全部区域天气数据\"\"\"\n", "    print(\"开始处理全部区域天气数据...\")\n", "    all_dfs = []\n", "    \n", "    for i, file in enumerate(regional_files):\n", "        print(f\"处理文件 {i+1}/{len(regional_files)}: {os.path.basename(file)}\")\n", "        df = standardize_regional_weather(file)\n", "        if not df.empty:\n", "            all_dfs.append(df)\n", "    \n", "    if all_dfs:\n", "        merged_df = pd.concat(all_dfs, ignore_index=True)\n", "        print(f\"合并完成，共 {len(merged_df)} 行\")\n", "        \n", "        # 保存数据\n", "        save_standardized_data(merged_df, \"weather_regional_merged\")\n", "        \n", "        # 返回合并后的数据\n", "        return merged_df\n", "    else:\n", "        print(\"没有成功处理的数据\")\n", "        return None\n", "\n", "# 处理全部城市24小时数据\n", "def process_all_city_24h_weather():\n", "    \"\"\"处理全部城市24小时数据\"\"\"\n", "    print(\"开始处理全部城市24小时数据...\")\n", "    all_dfs = []\n", "    \n", "    for i, file in enumerate(city_24h_files):\n", "        print(f\"处理文件 {i+1}/{len(city_24h_files)}: {os.path.basename(file)}\")\n", "        df = standardize_city_24h_weather(file)\n", "        if not df.empty:\n", "            all_dfs.append(df)\n", "    \n", "    if all_dfs:\n", "        merged_df = pd.concat(all_dfs, ignore_index=True)\n", "        print(f\"合并完成，共 {len(merged_df)} 行\")\n", "        \n", "        # 保存数据\n", "        save_standardized_data(merged_df, \"weather_city_24h_merged\")\n", "        \n", "        # 返回合并后的数据\n", "        return merged_df\n", "    else:\n", "        print(\"没有成功处理的数据\")\n", "        return None\n", "\n", "# 运行全量处理\n", "# 注意：这可能需要较长时间，可以根据需要注释掉\n", "print(\"是否运行全量天气数据处理？(可能需要较长时间)\")\n", "print(\"如需运行，请取消下面代码的注释\")\n", "\n", "# regional_merged = process_all_regional_weather()\n", "# city_24h_merged = process_all_city_24h_weather()\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 5. 生成合并报告\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 生成合并报告\n", "def generate_merge_report(regional_df=None, city_24h_df=None):\n", "    \"\"\"生成合并报告\"\"\"\n", "    report_file = PROCESSED_DATA_DIR / \"weather_merge_report.txt\"\n", "    \n", "    with open(report_file, 'w', encoding='utf-8') as f:\n", "        f.write(\"天气数据合并报告\\n\")\n", "        f.write(\"=\" * 80 + \"\\n\\n\")\n", "        \n", "        # 区域数据统计\n", "        if regional_df is not None and not regional_df.empty:\n", "            f.write(\"区域天气数据统计:\\n\")\n", "            f.write(f\"总行数: {len(regional_df)}\\n\")\n", "            f.write(f\"总列数: {len(regional_df.columns)}\\n\")\n", "            f.write(f\"列名: {list(regional_df.columns)}\\n\\n\")\n", "            \n", "            # 按区域统计\n", "            f.write(\"按区域统计:\\n\")\n", "            if 'region' in regional_df.columns:\n", "                region_counts = regional_df['region'].value_counts()\n", "                for region, count in region_counts.items():\n", "                    f.write(f\"  {region}: {count} 行\\n\")\n", "            f.write(\"\\n\")\n", "            \n", "            # 按年份统计\n", "            f.write(\"按年份统计:\\n\")\n", "            if 'year' in regional_df.columns:\n", "                year_counts = regional_df['year'].value_counts()\n", "                for year, count in year_counts.items():\n", "                    f.write(f\"  {year}: {count} 行\\n\")\n", "            f.write(\"\\n\")\n", "            \n", "            # 缺失值统计\n", "            f.write(\"缺失值统计:\\n\")\n", "            missing = regional_df.isnull().sum()\n", "            for col, count in missing.items():\n", "                if count > 0:\n", "                    percent = (count / len(regional_df)) * 100\n", "                    f.write(f\"  {col}: {count} 行 ({percent:.2f}%)\\n\")\n", "            f.write(\"\\n\")\n", "        \n", "        # 城市24小时数据统计\n", "        if city_24h_df is not None and not city_24h_df.empty:\n", "            f.write(\"城市24小时数据统计:\\n\")\n", "            f.write(f\"总行数: {len(city_24h_df)}\\n\")\n", "            f.write(f\"总列数: {len(city_24h_df.columns)}\\n\")\n", "            f.write(f\"列名: {list(city_24h_df.columns)}\\n\\n\")\n", "            \n", "            # 按城市统计\n", "            f.write(\"按城市统计:\\n\")\n", "            if 'city' in city_24h_df.columns:\n", "                city_counts = city_24h_df['city'].value_counts()\n", "                for city, count in city_counts.items():\n", "                    f.write(f\"  {city}: {count} 行\\n\")\n", "            f.write(\"\\n\")\n", "            \n", "            # 按年份统计\n", "            f.write(\"按年份统计:\\n\")\n", "            if 'year' in city_24h_df.columns:\n", "                year_counts = city_24h_df['year'].value_counts()\n", "                for year, count in year_counts.items():\n", "                    f.write(f\"  {year}: {count} 行\\n\")\n", "            f.write(\"\\n\")\n", "            \n", "            # 缺失值统计\n", "            f.write(\"缺失值统计:\\n\")\n", "            missing = city_24h_df.isnull().sum()\n", "            for col, count in missing.items():\n", "                if count > 0:\n", "                    percent = (count / len(city_24h_df)) * 100\n", "                    f.write(f\"  {col}: {count} 行 ({percent:.2f}%)\\n\")\n", "        \n", "    print(f\"合并报告已保存至: {report_file}\")\n", "    return report_file\n", "\n", "# 生成示例报告\n", "if 'merged_df' in locals() and 'city_24h_merged_df' in locals():\n", "    report_file = generate_merge_report(merged_df, city_24h_merged_df)\n", "    \n", "    # 显示报告内容\n", "    if os.path.exists(report_file):\n", "        with open(report_file, 'r', encoding='utf-8') as f:\n", "            print(f.read())\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 6. 数据可视化\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 区域天气数据可视化\n", "def visualize_regional_weather(df):\n", "    \"\"\"区域天气数据可视化\"\"\"\n", "    if df is None or df.empty:\n", "        print(\"没有数据可视化\")\n", "        return\n", "    \n", "    # 设置图形大小\n", "    plt.figure(figsize=(14, 8))\n", "    \n", "    # 1. 按区域统计数据量\n", "    if 'region' in df.columns:\n", "        plt.subplot(2, 2, 1)\n", "        region_counts = df['region'].value_counts().sort_values(ascending=False)\n", "        sns.barplot(x=region_counts.index[:10], y=region_counts.values[:10])\n", "        plt.title('按区域统计数据量 (前10个)')\n", "        plt.xticks(rotation=45)\n", "        plt.tight_layout()\n", "    \n", "    # 2. 温度分布\n", "    if 'temperature' in df.columns:\n", "        plt.subplot(2, 2, 2)\n", "        sns.histplot(df['temperature'].dropna(), kde=True)\n", "        plt.title('温度分布')\n", "        plt.xlabel('温度 (°C)')\n", "        plt.tight_layout()\n", "    \n", "    # 3. 湿度分布\n", "    if 'humidity' in df.columns:\n", "        plt.subplot(2, 2, 3)\n", "        sns.histplot(df['humidity'].dropna(), kde=True)\n", "        plt.title('湿度分布')\n", "        plt.xlabel('湿度 (%)')\n", "        plt.tight_layout()\n", "    \n", "    # 4. 温度随时间变化 (取一个区域样本)\n", "    if 'time' in df.columns and 'temperature' in df.columns and 'region' in df.columns:\n", "        plt.subplot(2, 2, 4)\n", "        \n", "        # 选择一个有足够数据的区域\n", "        sample_region = df['region'].value_counts().index[0]\n", "        sample_df = df[df['region'] == sample_region].sort_values('time')\n", "        \n", "        # 确保时间列是datetime类型\n", "        if sample_df['time'].dtype != 'datetime64[ns]':\n", "            sample_df['time'] = pd.to_datetime(sample_df['time'], errors='coerce')\n", "        \n", "        # 绘制温度随时间变化\n", "        plt.plot(sample_df['time'], sample_df['temperature'])\n", "        plt.title(f'{sample_region}区域温度随时间变化')\n", "        plt.xlabel('时间')\n", "        plt.ylabel('温度 (°C)')\n", "        plt.xticks(rotation=45)\n", "        plt.tight_layout()\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "# 城市24小时数据可视化\n", "def visualize_city_24h_weather(df):\n", "    \"\"\"城市24小时数据可视化\"\"\"\n", "    if df is None or df.empty:\n", "        print(\"没有数据可视化\")\n", "        return\n", "    \n", "    # 设置图形大小\n", "    plt.figure(figsize=(14, 8))\n", "    \n", "    # 1. 按城市统计数据量\n", "    if 'city' in df.columns:\n", "        plt.subplot(2, 2, 1)\n", "        city_counts = df['city'].value_counts().sort_values(ascending=False)\n", "        sns.barplot(x=city_counts.index[:10], y=city_counts.values[:10])\n", "        plt.title('按城市统计数据量 (前10个)')\n", "        plt.xticks(rotation=45)\n", "        plt.tight_layout()\n", "    \n", "    # 2. AQI分布\n", "    if 'aqi' in df.columns:\n", "        plt.subplot(2, 2, 2)\n", "        sns.histplot(df['aqi'].dropna(), kde=True)\n", "        plt.title('AQI分布')\n", "        plt.xlabel('AQI')\n", "        plt.tight_layout()\n", "    \n", "    # 3. PM2.5分布\n", "    if 'pm25' in df.columns:\n", "        plt.subplot(2, 2, 3)\n", "        sns.histplot(df['pm25'].dropna(), kde=True)\n", "        plt.title('PM2.5分布')\n", "        plt.xlabel('PM2.5 (μg/m³)')\n", "        plt.tight_layout()\n", "    \n", "    # 4. AQI类别分布\n", "    if 'aqi_category' in df.columns:\n", "        plt.subplot(2, 2, 4)\n", "        category_counts = df['aqi_category'].value_counts()\n", "        sns.barplot(x=category_counts.index, y=category_counts.values)\n", "        plt.title('AQI类别分布')\n", "        plt.xticks(rotation=45)\n", "        plt.tight_layout()\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "# 可视化示例数据\n", "if 'merged_df' in locals() and not merged_df.empty:\n", "    visualize_regional_weather(merged_df)\n", "    \n", "if 'city_24h_merged_df' in locals() and not city_24h_merged_df.empty:\n", "    visualize_city_24h_weather(city_24h_merged_df)\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 7. 后续步骤\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "完成本notebook的数据加载与探索后，后续步骤包括：\n", "\n", "1. **负荷数据与天气数据的关联分析**\n", "   - 基于时间和区域将负荷数据与天气数据关联\n", "   - 分析温度与负荷的相关性\n", "   - 识别空调负荷敏感区域和时段\n", "\n", "2. **特征工程**\n", "   - 提取时间特征（小时、日、周、季节等）\n", "   - 计算温度敏感度指标\n", "   - 构建滞后特征（如前一天负荷、前一周同期负荷等）\n", "   - 创建空调负荷识别特征\n", "\n", "3. **模型构建**\n", "   - 空调负荷识别模型\n", "   - 柔性调控能力评估模型\n", "   - 模型验证与调优\n", "\n", "4. **结果可视化与报告**\n", "   - 空调负荷时空分布可视化\n", "   - 柔性调控能力评估报告\n", "   - 决策支持仪表板\n", "\"\"\"\n"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}