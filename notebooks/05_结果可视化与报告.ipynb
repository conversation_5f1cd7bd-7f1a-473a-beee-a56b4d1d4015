{"cells": [{"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["# 空调负荷柔性调控能力分析系统 - 结果可视化与报告\n", "\n", "本notebook用于对空调负荷识别和柔性调控能力评估结果进行可视化和报告生成。\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 导入必要的库\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import os\n", "from pathlib import Path\n", "import warnings\n", "import joblib\n", "from datetime import datetime, timedelta\n", "import plotly.express as px\n", "import plotly.graph_objects as go\n", "from plotly.subplots import make_subplots\n", "import matplotlib.dates as mdates\n", "\n", "# 设置中文显示\n", "plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签\n", "plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号\n", "warnings.filterwarnings('ignore')\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 定义数据路径\n", "DATA_DIR = Path(\"../data\")\n", "PROCESSED_DATA_DIR = DATA_DIR / \"processed\"\n", "MODELS_DIR = Path(\"../models\")\n", "RESULTS_DIR = Path(\"../results\")\n", "\n", "# 确保目录存在\n", "PROCESSED_DATA_DIR.mkdir(parents=True, exist_ok=True)\n", "MODELS_DIR.mkdir(parents=True, exist_ok=True)\n", "RESULTS_DIR.mkdir(parents=True, exist_ok=True)\n", "\n", "print(f\"处理后数据目录: {PROCESSED_DATA_DIR}\")\n", "print(f\"模型目录: {MODELS_DIR}\")\n", "print(f\"结果目录: {RESULTS_DIR}\")\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 1. 加载评估结果数据\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 加载数据\n", "def load_data(file_path, file_type='parquet'):\n", "    \"\"\"加载数据文件\"\"\"\n", "    try:\n", "        if file_type == 'parquet':\n", "            if Path(file_path).with_suffix('.parquet').exists():\n", "                return pd.read_parquet(Path(file_path).with_suffix('.parquet'))\n", "        elif file_type == 'csv':\n", "            if Path(file_path).with_suffix('.csv').exists():\n", "                return pd.read_csv(Path(file_path).with_suffix('.csv'))\n", "        \n", "        print(f\"文件不存在: {file_path}\")\n", "        return pd.DataFrame()\n", "    except Exception as e:\n", "        print(f\"加载数据时出错: {e}\")\n", "        return pd.DataFrame()\n", "\n", "# 加载柔性调控能力评估结果\n", "flexibility_df = load_data(PROCESSED_DATA_DIR / \"flexibility_assessment\", file_type='parquet')\n", "\n", "# 如果没有柔性调控能力评估结果，则加载特征工程后的数据\n", "if flexibility_df.empty:\n", "    print(\"未找到柔性调控能力评估结果，加载特征工程后的数据...\")\n", "    flexibility_df = load_data(PROCESSED_DATA_DIR / \"features_engineered\", file_type='parquet')\n", "    \n", "    # 如果没有特征工程后的数据，则加载原始数据\n", "    if flexibility_df.empty:\n", "        print(\"未找到特征工程后的数据，加载原始数据...\")\n", "        # 尝试加载合并后的数据\n", "        flexibility_df = load_data(PROCESSED_DATA_DIR / \"load_weather_regional_merged\", file_type='parquet')\n", "        \n", "        # 如果没有合并后的数据，则分别加载负荷数据和天气数据\n", "        if flexibility_df.empty:\n", "            print(\"未找到合并后的数据，加载原始负荷数据...\")\n", "            flexibility_df = load_data(DATA_DIR / \"raw\" / \"load_data\", file_type='parquet')\n", "\n", "# 显示数据基本信息\n", "if not flexibility_df.empty:\n", "    print(f\"数据维度: {flexibility_df.shape}\")\n", "    print(f\"列名: {flexibility_df.columns.tolist()}\")\n", "    display(flexibility_df.head())\n", "    \n", "    # 检查是否包含柔性调控能力评估结果\n", "    flexibility_cols = [col for col in flexibility_df.columns if 'flexibility' in col.lower()]\n", "    if flexibility_cols:\n", "        print(f\"\\n包含柔性调控能力评估结果列: {flexibility_cols}\")\n", "    else:\n", "        print(\"\\n不包含柔性调控能力评估结果列\")\n", "else:\n", "    print(\"没有可用的数据\")\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 2. 空调负荷时空分布可视化\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 空调负荷时空分布可视化\n", "def visualize_ac_load_distribution(df):\n", "    \"\"\"\n", "    空调负荷时空分布可视化\n", "    \n", "    Args:\n", "        df: 包含空调负荷数据的DataFrame\n", "    \"\"\"\n", "    if df is None or df.empty:\n", "        print(\"没有数据可视化\")\n", "        return\n", "    \n", "    # 确定空调负荷列\n", "    ac_load_col = None\n", "    for col in df.columns:\n", "        if 'ac_load' in col.lower():\n", "            ac_load_col = col\n", "            break\n", "    \n", "    # 确定负荷列\n", "    load_col = None\n", "    for col in df.columns:\n", "        if 'load_value' in col.lower() or 'load' in col.lower():\n", "            if 'ac_load' not in col.lower():  # 排除空调负荷列\n", "                load_col = col\n", "                break\n", "    \n", "    # 确定温度列\n", "    temp_col = None\n", "    for col in df.columns:\n", "        if 'temperature' in col.lower() or 'temp' in col.lower():\n", "            temp_col = col\n", "            break\n", "    \n", "    # 确定时间列\n", "    time_col = None\n", "    for col in df.columns:\n", "        if 'timestamp' in col.lower() or 'time' in col.lower() or 'date' in col.lower():\n", "            time_col = col\n", "            break\n", "    \n", "    # 确定区域列\n", "    region_col = None\n", "    for col in df.columns:\n", "        if 'region' in col.lower():\n", "            region_col = col\n", "            break\n", "    \n", "    if ac_load_col is None:\n", "        print(\"未找到空调负荷列\")\n", "        return\n", "    \n", "    print(f\"使用空调负荷列: {ac_load_col}\")\n", "    if load_col:\n", "        print(f\"使用负荷列: {load_col}\")\n", "    if temp_col:\n", "        print(f\"使用温度列: {temp_col}\")\n", "    if time_col:\n", "        print(f\"使用时间列: {time_col}\")\n", "    if region_col:\n", "        print(f\"使用区域列: {region_col}\")\n", "    \n", "    # 1. 空调负荷时间分布\n", "    if time_col and 'hour' in df.columns:\n", "        # 按小时统计平均空调负荷\n", "        hourly_ac_load = df.groupby('hour')[ac_load_col].mean().reset_index()\n", "        \n", "        plt.figure(figsize=(12, 6))\n", "        plt.plot(hourly_ac_load['hour'], hourly_ac_load[ac_load_col], marker='o')\n", "        plt.title('空调负荷小时分布')\n", "        plt.xlabel('小时')\n", "        plt.ylabel('平均空调负荷')\n", "        plt.grid(True)\n", "        plt.xticks(range(0, 24))\n", "        plt.show()\n", "        \n", "        # 按小时统计空调负荷占比\n", "        if load_col:\n", "            hourly_load = df.groupby('hour')[load_col].mean().reset_index()\n", "            hourly_ac_ratio = pd.merge(hourly_ac_load, hourly_load, on='hour')\n", "            hourly_ac_ratio['ac_ratio'] = hourly_ac_ratio[ac_load_col] / hourly_ac_ratio[load_col]\n", "            \n", "            plt.figure(figsize=(12, 6))\n", "            plt.plot(hourly_ac_ratio['hour'], hourly_ac_ratio['ac_ratio'], marker='o', color='orange')\n", "            plt.title('空调负荷占比小时分布')\n", "            plt.xlabel('小时')\n", "            plt.ylabel('空调负荷占比')\n", "            plt.grid(True)\n", "            plt.xticks(range(0, 24))\n", "            plt.ylim(0, 1)\n", "            plt.show()\n", "    \n", "    # 2. 空调负荷区域分布\n", "    if region_col:\n", "        # 按区域统计平均空调负荷\n", "        regional_ac_load = df.groupby(region_col)[ac_load_col].mean().reset_index()\n", "        regional_ac_load = regional_ac_load.sort_values(ac_load_col, ascending=False)\n", "        \n", "        plt.figure(figsize=(14, 6))\n", "        sns.barplot(x=region_col, y=ac_load_col, data=regional_ac_load)\n", "        plt.title('空调负荷区域分布')\n", "        plt.xlabel('区域')\n", "        plt.ylabel('平均空调负荷')\n", "        plt.xticks(rotation=45)\n", "        plt.tight_layout()\n", "        plt.show()\n", "        \n", "        # 按区域统计空调负荷占比\n", "        if load_col:\n", "            regional_load = df.groupby(region_col)[load_col].mean().reset_index()\n", "            regional_ac_ratio = pd.merge(regional_ac_load, regional_load, on=region_col)\n", "            regional_ac_ratio['ac_ratio'] = regional_ac_ratio[ac_load_col] / regional_ac_ratio[load_col]\n", "            regional_ac_ratio = regional_ac_ratio.sort_values('ac_ratio', ascending=False)\n", "            \n", "            plt.figure(figsize=(14, 6))\n", "            sns.barplot(x=region_col, y='ac_ratio', data=regional_ac_ratio)\n", "            plt.title('空调负荷占比区域分布')\n", "            plt.xlabel('区域')\n", "            plt.ylabel('空调负荷占比')\n", "            plt.xticks(rotation=45)\n", "            plt.ylim(0, 1)\n", "            plt.tight_layout()\n", "            plt.show()\n", "    \n", "    # 3. 空调负荷与温度关系\n", "    if temp_col:\n", "        plt.figure(figsize=(10, 6))\n", "        plt.scatter(df[temp_col], df[ac_load_col], alpha=0.5)\n", "        plt.title('空调负荷与温度关系')\n", "        plt.xlabel('温度')\n", "        plt.ylabel('空调负荷')\n", "        plt.grid(True)\n", "        plt.show()\n", "        \n", "        # 按温度区间统计平均空调负荷\n", "        df['temp_bin'] = pd.cut(df[temp_col], bins=10)\n", "        temp_bin_ac_load = df.groupby('temp_bin')[ac_load_col].mean().reset_index()\n", "        \n", "        plt.figure(figsize=(12, 6))\n", "        sns.barplot(x='temp_bin', y=ac_load_col, data=temp_bin_ac_load)\n", "        plt.title('温度区间空调负荷分布')\n", "        plt.xlabel('温度区间')\n", "        plt.ylabel('平均空调负荷')\n", "        plt.xticks(rotation=45)\n", "        plt.tight_layout()\n", "        plt.show()\n", "    \n", "    # 4. 空调负荷热力图\n", "    if 'hour' in df.columns and region_col:\n", "        # 按小时和区域统计平均空调负荷\n", "        heatmap_data = df.pivot_table(\n", "            values=ac_load_col,\n", "            index='hour',\n", "            columns=region_col,\n", "            aggfunc='mean'\n", "        )\n", "        \n", "        plt.figure(figsize=(16, 8))\n", "        sns.heatmap(heatmap_data, cmap='YlOrRd', annot=False)\n", "        plt.title('空调负荷时空热力图')\n", "        plt.xlabel('区域')\n", "        plt.ylabel('小时')\n", "        plt.tight_layout()\n", "        plt.show()\n", "    \n", "    # 5. 空调负荷时间序列\n", "    if time_col and region_col:\n", "        # 确保时间列是datetime类型\n", "        if df[time_col].dtype != 'datetime64[ns]':\n", "            try:\n", "                df[time_col] = pd.to_datetime(df[time_col], errors='coerce')\n", "            except:\n", "                print(f\"无法将 {time_col} 转换为datetime类型\")\n", "                return\n", "        \n", "        # 选择几个主要区域\n", "        top_regions = df.groupby(region_col)[ac_load_col].mean().sort_values(ascending=False).head(5).index\n", "        \n", "        plt.figure(figsize=(14, 8))\n", "        \n", "        for region in top_regions:\n", "            region_data = df[df[region_col] == region].sort_values(time_col)\n", "            plt.plot(region_data[time_col], region_data[ac_load_col], label=region)\n", "        \n", "        plt.title('主要区域空调负荷时间序列')\n", "        plt.xlabel('时间')\n", "        plt.ylabel('空调负荷')\n", "        plt.legend()\n", "        plt.grid(True)\n", "        plt.tight_layout()\n", "        plt.show()\n", "\n", "# 应用空调负荷时空分布可视化\n", "if not flexibility_df.empty:\n", "    visualize_ac_load_distribution(flexibility_df)\n", "else:\n", "    print(\"没有可用的数据\")\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 3. 柔性调控能力评估可视化\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 柔性调控能力评估可视化\n", "def visualize_flexibility_assessment(df):\n", "    \"\"\"\n", "    柔性调控能力评估可视化\n", "    \n", "    Args:\n", "        df: 包含柔性调控能力评估结果的DataFrame\n", "    \"\"\"\n", "    if df is None or df.empty:\n", "        print(\"没有数据可视化\")\n", "        return\n", "    \n", "    # 确定柔性调控能力列\n", "    flexibility_col = None\n", "    for col in df.columns:\n", "        if 'flexibility_total' in col.lower():\n", "            flexibility_col = col\n", "            break\n", "    \n", "    if flexibility_col is None:\n", "        for col in df.columns:\n", "            if 'flexibility' in col.lower():\n", "                flexibility_col = col\n", "                break\n", "    \n", "    # 确定负荷列\n", "    load_col = None\n", "    for col in df.columns:\n", "        if 'load_value' in col.lower() or 'load' in col.lower():\n", "            if 'ac_load' not in col.lower() and 'flexibility' not in col.lower():\n", "                load_col = col\n", "                break\n", "    \n", "    # 确定时间列\n", "    time_col = None\n", "    for col in df.columns:\n", "        if 'timestamp' in col.lower() or 'time' in col.lower() or 'date' in col.lower():\n", "            time_col = col\n", "            break\n", "    \n", "    # 确定区域列\n", "    region_col = None\n", "    for col in df.columns:\n", "        if 'region' in col.lower():\n", "            region_col = col\n", "            break\n", "    \n", "    if flexibility_col is None:\n", "        print(\"未找到柔性调控能力列\")\n", "        return\n", "    \n", "    print(f\"使用柔性调控能力列: {flexibility_col}\")\n", "    if load_col:\n", "        print(f\"使用负荷列: {load_col}\")\n", "    if time_col:\n", "        print(f\"使用时间列: {time_col}\")\n", "    if region_col:\n", "        print(f\"使用区域列: {region_col}\")\n", "    \n", "    # 1. 柔性调控能力时间分布\n", "    if time_col and 'hour' in df.columns:\n", "        # 按小时统计平均柔性调控能力\n", "        hourly_flexibility = df.groupby('hour')[flexibility_col].mean().reset_index()\n", "        \n", "        plt.figure(figsize=(12, 6))\n", "        plt.plot(hourly_flexibility['hour'], hourly_flexibility[flexibility_col], marker='o')\n", "        plt.title('柔性调控能力小时分布')\n", "        plt.xlabel('小时')\n", "        plt.ylabel('平均柔性调控能力')\n", "        plt.grid(True)\n", "        plt.xticks(range(0, 24))\n", "        plt.show()\n", "        \n", "        # 按小时统计柔性调控能力占比\n", "        if load_col:\n", "            hourly_load = df.groupby('hour')[load_col].mean().reset_index()\n", "            hourly_flexibility_ratio = pd.merge(hourly_flexibility, hourly_load, on='hour')\n", "            hourly_flexibility_ratio['flexibility_ratio'] = hourly_flexibility_ratio[flexibility_col] / hourly_flexibility_ratio[load_col]\n", "            \n", "            plt.figure(figsize=(12, 6))\n", "            plt.plot(hourly_flexibility_ratio['hour'], hourly_flexibility_ratio['flexibility_ratio'], marker='o', color='green')\n", "            plt.title('柔性调控能力占比小时分布')\n", "            plt.xlabel('小时')\n", "            plt.ylabel('柔性调控能力占比')\n", "            plt.grid(True)\n", "            plt.xticks(range(0, 24))\n", "            plt.ylim(0, 0.5)  # 假设柔性调控能力不超过总负荷的50%\n", "            plt.show()\n", "    \n", "    # 2. 柔性调控能力区域分布\n", "    if region_col:\n", "        # 按区域统计平均柔性调控能力\n", "        regional_flexibility = df.groupby(region_col)[flexibility_col].mean().reset_index()\n", "        regional_flexibility = regional_flexibility.sort_values(flexibility_col, ascending=False)\n", "        \n", "        plt.figure(figsize=(14, 6))\n", "        sns.barplot(x=region_col, y=flexibility_col, data=regional_flexibility)\n", "        plt.title('柔性调控能力区域分布')\n", "        plt.xlabel('区域')\n", "        plt.ylabel('平均柔性调控能力')\n", "        plt.xticks(rotation=45)\n", "        plt.tight_layout()\n", "        plt.show()\n", "        \n", "        # 按区域统计柔性调控能力占比\n", "        if load_col:\n", "            regional_load = df.groupby(region_col)[load_col].mean().reset_index()\n", "            regional_flexibility_ratio = pd.merge(regional_flexibility, regional_load, on=region_col)\n", "            regional_flexibility_ratio['flexibility_ratio'] = regional_flexibility_ratio[flexibility_col] / regional_flexibility_ratio[load_col]\n", "            regional_flexibility_ratio = regional_flexibility_ratio.sort_values('flexibility_ratio', ascending=False)\n", "            \n", "            plt.figure(figsize=(14, 6))\n", "            sns.barplot(x=region_col, y='flexibility_ratio', data=regional_flexibility_ratio)\n", "            plt.title('柔性调控能力占比区域分布')\n", "            plt.xlabel('区域')\n", "            plt.ylabel('柔性调控能力占比')\n", "            plt.xticks(rotation=45)\n", "            plt.ylim(0, 0.5)  # 假设柔性调控能力不超过总负荷的50%\n", "            plt.tight_layout()\n", "            plt.show()\n", "    \n", "    # 3. 柔性调控能力热力图\n", "    if 'hour' in df.columns and region_col:\n", "        # 按小时和区域统计平均柔性调控能力\n", "        heatmap_data = df.pivot_table(\n", "            values=flexibility_col,\n", "            index='hour',\n", "            columns=region_col,\n", "            aggfunc='mean'\n", "        )\n", "        \n", "        plt.figure(figsize=(16, 8))\n", "        sns.heatmap(heatmap_data, cmap='YlGnBu', annot=False)\n", "        plt.title('柔性调控能力时空热力图')\n", "        plt.xlabel('区域')\n", "        plt.ylabel('小时')\n", "        plt.tight_layout()\n", "        plt.show()\n", "    \n", "    # 4. 柔性调控能力时间序列\n", "    if time_col and region_col:\n", "        # 确保时间列是datetime类型\n", "        if df[time_col].dtype != 'datetime64[ns]':\n", "            try:\n", "                df[time_col] = pd.to_datetime(df[time_col], errors='coerce')\n", "            except:\n", "                print(f\"无法将 {time_col} 转换为datetime类型\")\n", "                return\n", "        \n", "        # 选择几个主要区域\n", "        top_regions = df.groupby(region_col)[flexibility_col].mean().sort_values(ascending=False).head(5).index\n", "        \n", "        plt.figure(figsize=(14, 8))\n", "        \n", "        for region in top_regions:\n", "            region_data = df[df[region_col] == region].sort_values(time_col)\n", "            plt.plot(region_data[time_col], region_data[flexibility_col], label=region)\n", "        \n", "        plt.title('主要区域柔性调控能力时间序列')\n", "        plt.xlabel('时间')\n", "        plt.ylabel('柔性调控能力')\n", "        plt.legend()\n", "        plt.grid(True)\n", "        plt.tight_layout()\n", "        plt.show()\n", "    \n", "    # 5. 柔性调控能力与负荷对比\n", "    if load_col:\n", "        # 按小时统计平均负荷和柔性调控能力\n", "        hourly_data = df.groupby('hour').agg({\n", "            load_col: 'mean',\n", "            flexibility_col: 'mean'\n", "        }).reset_index()\n", "        \n", "        fig, ax1 = plt.subplots(figsize=(12, 6))\n", "        \n", "        # 负荷曲线\n", "        ax1.plot(hourly_data['hour'], hourly_data[load_col], 'b-', marker='o', label='总负荷')\n", "        ax1.set_xlabel('小时')\n", "        ax1.set_ylabel('负荷', color='b')\n", "        ax1.tick_params(axis='y', labelcolor='b')\n", "        ax1.set_xticks(range(0, 24))\n", "        \n", "        # 柔性调控能力曲线\n", "        ax2 = ax1.twinx()\n", "        ax2.plot(hourly_data['hour'], hourly_data[flexibility_col], 'g-', marker='s', label='柔性调控能力')\n", "        ax2.set_ylabel('柔性调控能力', color='g')\n", "        ax2.tick_params(axis='y', labelcolor='g')\n", "        \n", "        # 添加图例\n", "        lines1, labels1 = ax1.get_legend_handles_labels()\n", "        lines2, labels2 = ax2.get_legend_handles_labels()\n", "        ax1.legend(lines1 + lines2, labels1 + labels2, loc='upper left')\n", "        \n", "        plt.title('负荷与柔性调控能力小时对比')\n", "        plt.grid(True)\n", "        plt.tight_layout()\n", "        plt.show()\n", "\n", "# 应用柔性调控能力评估可视化\n", "if not flexibility_df.empty:\n", "    visualize_flexibility_assessment(flexibility_df)\n", "else:\n", "    print(\"没有可用的数据\")\n"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}