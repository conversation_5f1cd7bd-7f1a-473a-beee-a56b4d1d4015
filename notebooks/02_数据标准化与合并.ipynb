# 数据标准化与合并

本notebook用于处理负荷数据和天气数据的标准化，创建区域映射表，并将不同数据源合并为分析用的大宽表。

## 目标
1. 标准化负荷数据（统一列名、数据类型等）
2. 标准化天气数据（统一列名、数据类型等）
3. 创建区域映射表，建立不同数据源之间的关联
4. 合并负荷数据和天气数据，创建用于分析的大宽表


#!/usr/bin/env python
# coding: utf-8

# 导入必要的库
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import os
import glob
import re
from pathlib import Path
import logging
from typing import Dict, List, Optional, Tuple
import warnings

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

# 设置显示选项
pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', 100)
pd.set_option('display.width', 1000)

# 忽略警告
warnings.filterwarnings('ignore')

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


## 1. 定义数据标准化类

我们首先定义一个数据标准化类，用于处理负荷数据和天气数据的标准化。


class DataStandardizer:
    """数据标准化器类"""
    
    def __init__(self, data_root: str = "."):
        """
        初始化数据标准化器
        
        Args:
            data_root: 数据根目录
        """
        self.data_root = Path(data_root)
        
        # 创建标准化目录结构
        self.create_directory_structure()
        
        # 定义列名映射
        self.load_column_mapping = {
            'SubstationID': 'substation_id',
            'SubstationName': 'substation_name',
            'CircuitID': 'circuit_id',
            'CircuitName': 'circuit_name',
            'FeederID': 'feeder_id',
            'FeederName': 'feeder_name',
            'ConsumerID': 'consumer_id',
            'ConsumerName': 'consumer_name',
            'ConsumerType': 'consumer_type',
            'ConsumerAddress': 'consumer_address',
            'Timestamp': 'timestamp',
            'HourlyAvgLoad': 'hourly_avg_load',
            'HourlyMaxLoad': 'hourly_max_load',
            'HourlyMinLoad': 'hourly_min_load',
            'HourlyStdDev': 'hourly_std_dev',
            'HourlyActiveEnergy': 'hourly_active_energy',
            'HourlyReactiveEnergy': 'hourly_reactive_energy',
            'HourlyPowerFactor': 'hourly_power_factor',
            'HourlyLoadFactor': 'hourly_load_factor',
            'DailyMaxLoad': 'daily_max_load',
            'DailyMaxLoadTime': 'daily_max_load_time',
            'DailyMinLoad': 'daily_min_load',
            'DailyMinLoadTime': 'daily_min_load_time',
            'DailyAvgLoad': 'daily_avg_load',
            'DailyTotalEnergy': 'daily_total_energy',
            'DailyPeakValleyDiff': 'daily_peak_valley_diff',
            'DailyPeakValleyRatio': 'daily_peak_valley_ratio',
            'DailyLoadFactor': 'daily_load_factor',
            'DailyPeakLoad': 'daily_peak_load',
            'DailyNormalLoad': 'daily_normal_load',
            'DailyValleyLoad': 'daily_valley_load',
            'WeeklyMaxLoad': 'weekly_max_load',
            'WeeklyMaxLoadDate': 'weekly_max_load_date',
            'WeeklyMaxLoadTime': 'weekly_max_load_time',
            'WeeklyMinLoad': 'weekly_min_load',
            'WeeklyMinLoadDate': 'weekly_min_load_date',
            'WeeklyMinLoadTime': 'weekly_min_load_time',
            'WeeklyAvgLoad': 'weekly_avg_load',
            'WeeklyTotalEnergy': 'weekly_total_energy',
            'WeeklyPeakValleyDiff': 'weekly_peak_valley_diff',
            'WeeklyPeakValleyRatio': 'weekly_peak_valley_ratio',
            'WeeklyLoadFactor': 'weekly_load_factor',
            'WeekdayAvgLoad': 'weekday_avg_load',
            'WeekendAvgLoad': 'weekend_avg_load',
            'WeekdayWeekendRatio': 'weekday_weekend_ratio',
            'created_at': 'created_at',
            'updated_at': 'updated_at'
        }
        
        # 天气数据列名映射
        self.weather_column_mapping = {
            '经度(lon)': 'longitude',
            '纬度(lat)': 'latitude',
            '世界时(UTC)': 'timestamp_utc',
            '北京时(UTC+8)': 'timestamp',
            '气温(℃)': 'temperature',
            '气温2m(℃)': 'temperature',
            '相对湿度(%)': 'humidity',
            '地面气压(hPa)': 'pressure',
            '海平面气压(hPa)': 'pressure',
            '降水量(mm)': 'precipitation',
            '露点温度(℃)': 'dew_point',
            '经向风速(V,m/s)': 'wind_speed_v',
            '纬向风速(U,m/s)': 'wind_speed_u',
            ' 纬向风速(U,m/s)': 'wind_speed_u',
            '太阳辐射总强度(down,J/m2)': 'solar_radiation_down',
            '太阳辐射净强度(net,J/m2)': 'solar_radiation_net',
            '总太阳辐射度(down,J/m2)': 'solar_radiation_down',
            '净太阳辐射度(net,J/m2)': 'solar_radiation_net',
            '直接辐射(J/m2)': 'solar_radiation_direct',
            '紫外强度(J/m2)': 'uv_intensity',
            '总云量(tcc)': 'cloud_cover_total',
            '低层云量(lcc)': 'cloud_cover_low',
            '中层云量(mcc)': 'cloud_cover_mid',
            '高层云量(hcc)': 'cloud_cover_high',
            '云底高度(m)': 'cloud_base_height',
            '降雪量(mm)': 'snow_depth',
            '积雪深度(mm of water equivalent)': 'snow_depth',
            '积雪密度(kg/m3)': 'snow_density',
            '蒸发量(mm)': 'evaporation',
            '潜在蒸发量(mm)': 'potential_evaporation',
            '地表温度(℃)': 'surface_temperature',
            'K指数(K)': 'k_index',
            '对流可用位能(J/kg)': 'cape',
            '雷暴概率(TT，K)': 'thunderstorm_probability',
            '参考地名': 'region',
            '备注': 'region'
        }
    
    def create_directory_structure(self):
        """创建标准化目录结构"""
        directories = [
            "data/raw",
            "data/processed", 
            "data/interim",
            "原始数据/load",
            "原始数据/weather/regional",
            "原始数据/weather/city_24h",
            "原始数据/metadata"
        ]
        
        for directory in directories:
            (self.data_root / directory).mkdir(parents=True, exist_ok=True)
            logger.info(f"创建目录: {directory}")
    
    def standardize_load_data(self, input_file: str = None) -> pd.DataFrame:
        """
        标准化负荷数据
        
        Args:
            input_file: 输入文件路径，如果为None则尝试从processed目录加载
            
        Returns:
            标准化后的负荷数据DataFrame
        """
        logger.info("开始标准化负荷数据...")
        
        # 尝试加载预处理后的数据
        if input_file is None:
            processed_path = self.data_root / "data" / "processed" / "processed_load_data.parquet"
            if processed_path.exists():
                df = pd.read_parquet(processed_path)
                logger.info(f"从 {processed_path} 加载预处理数据")
            else:
                # 尝试加载原始数据
                input_path = self.data_root / "complete_load_analysis_data.csv"
                if not input_path.exists():
                    raise FileNotFoundError(f"负荷数据文件不存在: {input_path}")
                
                logger.info(f"从 {input_path} 加载原始数据")
                df = pd.read_csv(input_path)
        else:
            input_path = Path(input_file)
            if not input_path.exists():
                raise FileNotFoundError(f"负荷数据文件不存在: {input_path}")
            
            if input_path.suffix == '.parquet':
                df = pd.read_parquet(input_path)
            else:
                df = pd.read_csv(input_path)
            
            logger.info(f"从 {input_path} 加载数据")
        
        # 标准化列名
        df = df.rename(columns=self.load_column_mapping)
        
        # 处理时间列
        time_columns = ['timestamp', 'daily_max_load_time', 'daily_min_load_time', 
                        'weekly_max_load_date', 'weekly_max_load_time', 
                        'weekly_min_load_date', 'weekly_min_load_time',
                        'created_at', 'updated_at']
        
        for col in time_columns:
            if col in df.columns:
                df[col] = pd.to_datetime(df[col], errors='coerce')
        
        # 添加时间特征
        if 'timestamp' in df.columns:
            df['year'] = df['timestamp'].dt.year
            df['month'] = df['timestamp'].dt.month
            df['day'] = df['timestamp'].dt.day
            df['hour'] = df['timestamp'].dt.hour
            df['dayofweek'] = df['timestamp'].dt.dayofweek
            df['is_weekend'] = df['dayofweek'].isin([5, 6]).astype(int)
        
        logger.info(f"负荷数据标准化完成，形状: {df.shape}")
        return df
    
    def parse_weather_filename(self, filename: str) -> Tuple[str, str]:
        """解析天气文件名获取区域和年份"""
        name = filename.replace('.xls', '').replace('.xlsx', '')
        
        # 处理城市24小时数据
        if "城市24小时" in name:
            if "2023" in name:
                return "多城市", "2023"
            elif "24-25" in name:
                return "多城市", "2024-2025"
            else:
                return "多城市", "未知"
        
        # 处理区域数据
        pattern = r'^(.+?)(\d{2,4})$'
        match = re.match(pattern, name)
        
        if match:
            region = match.group(1).strip()
            year = match.group(2)
            
            if len(year) == 2:
                year = "20" + year
            
            return region, year
        
        return "未知", "未知"
    
    def standardize_weather_data(self, input_file: str = None) -> pd.DataFrame:
        """
        标准化天气数据
        
        Args:
            input_file: 输入文件路径，如果为None则尝试从processed目录加载
            
        Returns:
            标准化后的天气数据DataFrame
        """
        logger.info("开始标准化天气数据...")
        
        # 尝试加载预处理后的数据
        if input_file is None:
            processed_path = self.data_root / "data" / "processed" / "processed_weather_data.parquet"
            if processed_path.exists():
                df = pd.read_parquet(processed_path)
                logger.info(f"从 {processed_path} 加载预处理数据")
                return df
            else:
                # 如果没有预处理数据，则尝试从原始天气数据目录加载
                weather_dir = self.data_root / "天气数据"
                if not weather_dir.exists():
                    raise FileNotFoundError(f"天气数据目录不存在: {weather_dir}")
                
                # 获取所有天气数据文件
                weather_files = list(weather_dir.glob("*.xls")) + list(weather_dir.glob("*.xlsx"))
                
                if not weather_files:
                    raise FileNotFoundError(f"在 {weather_dir} 中未找到天气数据文件")
                
                all_weather_data = []
                
                for file_path in weather_files:
                    try:
                        # 解析文件名获取区域和年份
                        region, year = self.parse_weather_filename(file_path.stem)
                        
                        # 读取Excel文件
                        if file_path.suffix == '.xlsx':
                            df = pd.read_excel(file_path, engine='openpyxl')
                        else:
                            df = pd.read_excel(file_path, engine='xlrd')
                        
                        # 添加元数据
                        df['source_file'] = file_path.name
                        df['region'] = region
                        df['year'] = year
                        
                        # 标准化列名
                        renamed_columns = {}
                        for col in df.columns:
                            if col in self.weather_column_mapping:
                                renamed_columns[col] = self.weather_column_mapping[col]
                        
                        df = df.rename(columns=renamed_columns)
                        
                        # 处理时间列
                        if 'timestamp' in df.columns:
                            df['timestamp'] = pd.to_datetime(df['timestamp'], errors='coerce')
                            
                            # 添加时间特征
                            df['year'] = df['timestamp'].dt.year
                            df['month'] = df['timestamp'].dt.month
                            df['day'] = df['timestamp'].dt.day
                            df['hour'] = df['timestamp'].dt.hour
                            df['dayofweek'] = df['timestamp'].dt.dayofweek
                            df['is_weekend'] = df['dayofweek'].isin([5, 6]).astype(int)
                        
                        all_weather_data.append(df)
                        logger.info(f"处理 {file_path.name}: {df.shape}")
                        
                    except Exception as e:
                        logger.error(f"处理 {file_path} 失败: {e}")
                        continue
                
                if all_weather_data:
                    result = pd.concat(all_weather_data, ignore_index=True)
                    logger.info(f"天气数据标准化完成，总形状: {result.shape}")
                    return result
                else:
                    raise ValueError("没有成功加载任何天气数据")
        else:
            input_path = Path(input_file)
            if not input_path.exists():
                raise FileNotFoundError(f"天气数据文件不存在: {input_path}")
            
            if input_path.suffix == '.parquet':
                df = pd.read_parquet(input_path)
            else:
                df = pd.read_csv(input_path)
            
            logger.info(f"从 {input_path} 加载数据")
            return df
    
    def standardize_city_24h_data(self, input_file: str = None) -> pd.DataFrame:
        """
        标准化城市24小时数据
        
        Args:
            input_file: 输入文件路径，如果为None则尝试从processed目录加载
            
        Returns:
            标准化后的城市24小时数据DataFrame
        """
        logger.info("开始标准化城市24小时数据...")
        
        # 尝试加载预处理后的数据
        if input_file is None:
            processed_path = self.data_root / "data" / "processed" / "city_24h_data.parquet"
            if processed_path.exists():
                df = pd.read_parquet(processed_path)
                logger.info(f"从 {processed_path} 加载预处理数据")
                return df
            else:
                # 如果没有预处理数据，则尝试从原始天气数据目录加载
                weather_dir = self.data_root / "天气数据"
                if not weather_dir.exists():
                    raise FileNotFoundError(f"天气数据目录不存在: {weather_dir}")
                
                # 查找城市24小时数据文件
                city_24h_files = []
                for pattern in ["城市24小时-2023.xlsx", "城市24小时-24-25.xlsx"]:
                    file_path = weather_dir / pattern
                    if file_path.exists():
                        city_24h_files.append(file_path)
                
                if not city_24h_files:
                    logger.warning("未找到城市24小时数据文件")
                    return pd.DataFrame()
                
                all_data = []
                for file_path in city_24h_files:
                    try:
                        if file_path.suffix == '.xlsx':
                            df = pd.read_excel(file_path, engine='openpyxl')
                        else:
                            df = pd.read_excel(file_path, engine='xlrd')
                        
                        # 添加元数据
                        df['source_file'] = file_path.name
                        
                        # 标准化列名
                        renamed_columns = {}
                        for col in df.columns:
                            if col in self.weather_column_mapping:
                                renamed_columns[col] = self.weather_column_mapping[col]
                        
                        df = df.rename(columns=renamed_columns)
                        
                        # 处理日期和时间
                        if '日期' in df.columns and '小时' in df.columns:
                            df['timestamp'] = pd.to_datetime(df['日期'].astype(str) + ' ' + 
                                                          df['小时'].astype(str) + ':00:00', 
                                                          errors='coerce')
                            
                            # 添加时间特征
                            df['year'] = df['timestamp'].dt.year
                            df['month'] = df['timestamp'].dt.month
                            df['day'] = df['timestamp'].dt.day
                            df['hour'] = df['timestamp'].dt.hour
                            df['dayofweek'] = df['timestamp'].dt.dayofweek
                            df['is_weekend'] = df['dayofweek'].isin([5, 6]).astype(int)
                        
                        all_data.append(df)
                        logger.info(f"处理 {file_path.name}: {df.shape}")
                        
                    except Exception as e:
                        logger.error(f"处理 {file_path} 失败: {e}")
                        continue
                
                if all_data:
                    result = pd.concat(all_data, ignore_index=True)
                    logger.info(f"城市24小时数据标准化完成，总形状: {result.shape}")
                    return result
                else:
                    return pd.DataFrame()
        else:
            input_path = Path(input_file)
            if not input_path.exists():
                raise FileNotFoundError(f"城市24小时数据文件不存在: {input_path}")
            
            if input_path.suffix == '.parquet':
                df = pd.read_parquet(input_path)
            else:
                df = pd.read_csv(input_path)
            
            logger.info(f"从 {input_path} 加载数据")
            return df
    
    def save_standardized_data(self, df: pd.DataFrame, filename: str, 
                             data_type: str = "processed") -> None:
        """
        保存标准化后的数据
        
        Args:
            df: 标准化后的DataFrame
            filename: 输出文件名
            data_type: 数据类型，可选值为"raw"、"interim"或"processed"
        """
        output_dir = self.data_root / "data" / data_type
        output_dir.mkdir(parents=True, exist_ok=True)
        
        output_path = output_dir / f"{filename}.parquet"
        df.to_parquet(output_path, index=False)
        
        logger.info(f"数据已保存至: {output_path}")
    
    def generate_data_summary(self, df: pd.DataFrame, data_type: str) -> Dict:
        """
        生成数据摘要
        
        Args:
            df: 数据DataFrame
            data_type: 数据类型描述
            
        Returns:
            数据摘要字典
        """
        summary = {
            'data_type': data_type,
            'shape': df.shape,
            'columns': list(df.columns),
            'missing_values': df.isnull().sum().to_dict(),
            'data_types': df.dtypes.astype(str).to_dict()
        }
        
        # 如果有时间列，添加时间范围信息
        time_columns = [col for col in df.columns if 'time' in col.lower() or 'date' in col.lower()]
        if time_columns:
            time_ranges = {}
            for col in time_columns:
                if pd.api.types.is_datetime64_any_dtype(df[col]):
                    time_ranges[col] = {
                        'min': df[col].min().strftime('%Y-%m-%d %H:%M:%S') if not pd.isna(df[col].min()) else None,
                        'max': df[col].max().strftime('%Y-%m-%d %H:%M:%S') if not pd.isna(df[col].max()) else None
                    }
            summary['time_ranges'] = time_ranges
        
        return summary

# 创建数据标准化器实例
data_standardizer = DataStandardizer()


## 2. 定义天气数据合并类

接下来，我们定义一个天气数据合并类，用于合并不同来源的天气数据。


class WeatherMerger:
    """天气数据合并器类"""
    
    def __init__(self, data_root: str = "."):
        """
        初始化天气数据合并器
        
        Args:
            data_root: 数据根目录
        """
        self.data_root = Path(data_root)
        self.processed_dir = self.data_root / "data" / "processed"
        self.processed_dir.mkdir(parents=True, exist_ok=True)
        
        # 定义区域映射表
        self.region_mapping = {
            # 直辖市
            '北京': '北京市',
            '天津': '天津市',
            '上海': '上海市',
            '重庆': '重庆市',
            
            # 省会城市
            '石家庄': '河北省',
            '太原': '山西省',
            '呼和浩特': '内蒙古自治区',
            '沈阳': '辽宁省',
            '长春': '吉林省',
            '哈尔滨': '黑龙江省',
            '南京': '江苏省',
            '杭州': '浙江省',
            '合肥': '安徽省',
            '福州': '福建省',
            '南昌': '江西省',
            '济南': '山东省',
            '郑州': '河南省',
            '武汉': '湖北省',
            '长沙': '湖南省',
            '广州': '广东省',
            '南宁': '广西壮族自治区',
            '海口': '海南省',
            '成都': '四川省',
            '贵阳': '贵州省',
            '昆明': '云南省',
            '拉萨': '西藏自治区',
            '西安': '陕西省',
            '兰州': '甘肃省',
            '西宁': '青海省',
            '银川': '宁夏回族自治区',
            '乌鲁木齐': '新疆维吾尔自治区',
            
            # 其他主要城市
            '深圳': '广东省',
            '苏州': '江苏省',
            '青岛': '山东省',
            '大连': '辽宁省',
            '宁波': '浙江省',
            '厦门': '福建省',
            '无锡': '江苏省',
            '佛山': '广东省',
            '常州': '江苏省',
            '东莞': '广东省',
            '珠海': '广东省',
            '烟台': '山东省',
            '威海': '山东省',
            '镇江': '江苏省',
            '南通': '江苏省',
            '金华': '浙江省',
            '温州': '浙江省',
            '绍兴': '浙江省',
            '台州': '浙江省',
            '泉州': '福建省',
            '潍坊': '山东省',
            '淄博': '山东省',
            '临沂': '山东省',
            '济宁': '山东省',
            '洛阳': '河南省',
            '南阳': '河南省',
            '许昌': '河南省',
            '襄阳': '湖北省',
            '宜昌': '湖北省',
            '株洲': '湖南省',
            '湘潭': '湖南省',
            '衡阳': '湖南省',
            '岳阳': '湖南省',
            '汕头': '广东省',
            '湛江': '广东省',
            '中山': '广东省',
            '柳州': '广西壮族自治区',
            '桂林': '广西壮族自治区',
            '三亚': '海南省',
            '绵阳': '四川省',
            '德阳': '四川省',
            '遵义': '贵州省',
            '大理': '云南省',
            '丽江': '云南省',
            '咸阳': '陕西省',
            '宝鸡': '陕西省',
            '嘉兴': '浙江省',
            '芜湖': '安徽省',
            '徐州': '江苏省',
            '唐山': '河北省',
            '秦皇岛': '河北省',
            '邯郸': '河北省',
            '保定': '河北省',
            '沧州': '河北省',
            '廊坊': '河北省',
            '张家口': '河北省',
            '承德': '河北省',
            '邢台': '河北省',
            '衡水': '河北省',
            '大同': '山西省',
            '阳泉': '山西省',
            '长治': '山西省',
            '晋城': '山西省',
            '朔州': '山西省',
            '晋中': '山西省',
            '运城': '山西省',
            '忻州': '山西省',
            '临汾': '山西省',
            '吕梁': '山西省'
        }
    
    def process_time_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """处理时间列"""
        # 确保时间列是datetime类型
        if 'timestamp' in df.columns:
            df['timestamp'] = pd.to_datetime(df['timestamp'], errors='coerce')
            
            # 添加时间特征
            df['year'] = df['timestamp'].dt.year
            df['month'] = df['timestamp'].dt.month
            df['day'] = df['timestamp'].dt.day
            df['hour'] = df['timestamp'].dt.hour
            df['dayofweek'] = df['timestamp'].dt.dayofweek
            df['is_weekend'] = df['dayofweek'].isin([5, 6]).astype(int)
        
        return df
    
    def process_data_types(self, df: pd.DataFrame) -> pd.DataFrame:
        """处理数据类型"""
        # 处理数值列
        numeric_columns = [
            'temperature', 'humidity', 'pressure', 'precipitation',
            'dew_point', 'wind_speed_v', 'wind_speed_u', 'solar_radiation_down',
            'solar_radiation_net', 'solar_radiation_direct', 'uv_intensity',
            'cloud_cover_total', 'cloud_cover_low', 'cloud_cover_mid',
            'cloud_cover_high', 'cloud_base_height', 'snow_depth',
            'snow_density', 'evaporation', 'potential_evaporation',
            'surface_temperature', 'k_index', 'cape', 'thunderstorm_probability',
            'longitude', 'latitude'
        ]
        
        for col in numeric_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
        
        return df
    
    def merge_all_weather_data(self) -> pd.DataFrame:
        """
        合并所有天气数据
        
        Returns:
            合并后的天气数据DataFrame
        """
        logger.info("开始合并天气数据...")
        
        # 加载标准化后的区域天气数据
        weather_path = self.processed_dir / "standardized_weather_data.parquet"
        if not weather_path.exists():
            logger.warning(f"未找到标准化的天气数据文件: {weather_path}")
            weather_path = self.processed_dir / "processed_weather_data.parquet"
            if not weather_path.exists():
                raise FileNotFoundError(f"未找到天气数据文件: {weather_path}")
        
        regional_df = pd.read_parquet(weather_path)
        logger.info(f"加载区域天气数据: {regional_df.shape}")
        
        # 加载城市24小时数据
        city_24h_path = self.processed_dir / "standardized_city_24h_data.parquet"
        if not city_24h_path.exists():
            logger.warning(f"未找到标准化的城市24小时数据文件: {city_24h_path}")
            city_24h_path = self.processed_dir / "city_24h_data.parquet"
            if not city_24h_path.exists():
                logger.warning("未找到城市24小时数据文件，将只使用区域天气数据")
                city_24h_df = pd.DataFrame()
            else:
                city_24h_df = pd.read_parquet(city_24h_path)
        else:
            city_24h_df = pd.read_parquet(city_24h_path)
        
        if not city_24h_df.empty:
            logger.info(f"加载城市24小时数据: {city_24h_df.shape}")
            
            # 处理城市24小时数据
            if '城市' in city_24h_df.columns:
                # 添加区域信息
                city_24h_df['region'] = city_24h_df['城市'].apply(
                    lambda x: x if x not in self.region_mapping else x
                )
                
                # 添加省份信息
                city_24h_df['province'] = city_24h_df['城市'].apply(
                    lambda x: self.region_mapping.get(x, '未知')
                )
            
            # 确保时间列是datetime类型
            city_24h_df = self.process_time_columns(city_24h_df)
            
            # 处理数据类型
            city_24h_df = self.process_data_types(city_24h_df)
            
            # 合并区域天气数据和城市24小时数据
            # 先确定共有列
            common_columns = set(regional_df.columns).intersection(set(city_24h_df.columns))
            logger.info(f"区域天气数据和城市24小时数据的共有列: {common_columns}")
            
            # 合并数据
            merged_df = pd.concat([regional_df, city_24h_df], ignore_index=True, sort=False)
            logger.info(f"合并后的天气数据形状: {merged_df.shape}")
        else:
            merged_df = regional_df
            logger.info("未找到城市24小时数据，使用区域天气数据")
        
        # 处理合并后的数据
        merged_df = self.process_time_columns(merged_df)
        merged_df = self.process_data_types(merged_df)
        
        # 去除重复数据
        merged_df = merged_df.drop_duplicates(subset=['region', 'timestamp'], keep='first')
        logger.info(f"去重后的天气数据形状: {merged_df.shape}")
        
        # 生成合并报告
        self.generate_merge_report(regional_df, city_24h_df)
        
        return merged_df
    
    def generate_merge_report(self, regional_df: pd.DataFrame, city_24h_df: pd.DataFrame):
        """生成合并报告"""
        report = []
        
        report.append("# 天气数据合并报告")
        report.append("")
        
        # 区域天气数据信息
        report.append("## 区域天气数据")
        report.append(f"- 数据形状: {regional_df.shape}")
        report.append(f"- 区域数量: {regional_df['region'].nunique()}")
        if 'timestamp' in regional_df.columns:
            min_date = regional_df['timestamp'].min()
            max_date = regional_df['timestamp'].max()
            report.append(f"- 时间范围: {min_date} 至 {max_date}")
        report.append(f"- 列数量: {len(regional_df.columns)}")
        report.append("")
        
        # 城市24小时数据信息
        if not city_24h_df.empty:
            report.append("## 城市24小时数据")
            report.append(f"- 数据形状: {city_24h_df.shape}")
            if '城市' in city_24h_df.columns:
                report.append(f"- 城市数量: {city_24h_df['城市'].nunique()}")
            if 'timestamp' in city_24h_df.columns:
                min_date = city_24h_df['timestamp'].min()
                max_date = city_24h_df['timestamp'].max()
                report.append(f"- 时间范围: {min_date} 至 {max_date}")
            report.append(f"- 列数量: {len(city_24h_df.columns)}")
            report.append("")
        
        # 共有列信息
        if not city_24h_df.empty:
            common_columns = set(regional_df.columns).intersection(set(city_24h_df.columns))
            report.append("## 共有列")
            report.append(f"- 共有列数量: {len(common_columns)}")
            report.append(f"- 共有列: {sorted(list(common_columns))}")
            report.append("")
        
        # 写入报告文件
        report_path = self.data_root / "data" / "processed" / "weather_merge_report.txt"
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(report))
        
        logger.info(f"合并报告已保存至: {report_path}")
    
    def create_region_mapping_table(self) -> pd.DataFrame:
        """
        创建区域映射表
        
        Returns:
            区域映射表DataFrame
        """
        logger.info("创建区域映射表...")
        
        # 从区域映射字典创建DataFrame
        mapping_data = []
        for city, province in self.region_mapping.items():
            mapping_data.append({
                'city': city,
                'province': province
            })
        
        mapping_df = pd.DataFrame(mapping_data)
        logger.info(f"区域映射表创建完成，形状: {mapping_df.shape}")
        
        # 保存区域映射表
        mapping_path = self.data_root / "data" / "processed" / "region_mapping.parquet"
        mapping_df.to_parquet(mapping_path, index=False)
        logger.info(f"区域映射表已保存至: {mapping_path}")
        
        return mapping_df

# 创建天气数据合并器实例
weather_merger = WeatherMerger()


## 3. 标准化负荷数据

首先，我们对负荷数据进行标准化处理。


# 标准化负荷数据
standardized_load_df = data_standardizer.standardize_load_data()

# 显示标准化后的数据基本信息
print(f"标准化后的负荷数据形状: {standardized_load_df.shape}")
print(f"列名: {standardized_load_df.columns.tolist()}")

# 查看数据前几行
standardized_load_df.head()


# 检查标准化后的数据类型
standardized_load_df.dtypes


# 检查时间特征
if 'timestamp' in standardized_load_df.columns:
    print("时间特征示例:")
    time_features = ['timestamp', 'year', 'month', 'day', 'hour', 'dayofweek', 'is_weekend']
    display(standardized_load_df[time_features].head(10))


# 保存标准化后的负荷数据
data_standardizer.save_standardized_data(standardized_load_df, "standardized_load_data")


## 4. 标准化天气数据

接下来，我们对天气数据进行标准化处理。


# 标准化天气数据
standardized_weather_df = data_standardizer.standardize_weather_data()

# 显示标准化后的数据基本信息
print(f"标准化后的天气数据形状: {standardized_weather_df.shape}")
print(f"列名: {standardized_weather_df.columns.tolist()}")

# 查看数据前几行
standardized_weather_df.head()


# 检查区域信息
if 'region' in standardized_weather_df.columns:
    regions = standardized_weather_df['region'].unique()
    print(f"区域数量: {len(regions)}")
    print(f"区域列表: {sorted(regions)}")


# 保存标准化后的天气数据
data_standardizer.save_standardized_data(standardized_weather_df, "standardized_weather_data")


# 标准化城市24小时数据
standardized_city_24h_df = data_standardizer.standardize_city_24h_data()

# 显示标准化后的数据基本信息
if not standardized_city_24h_df.empty:
    print(f"标准化后的城市24小时数据形状: {standardized_city_24h_df.shape}")
    print(f"列名: {standardized_city_24h_df.columns.tolist()}")
    
    # 查看数据前几行
    display(standardized_city_24h_df.head())
    
    # 保存标准化后的城市24小时数据
    data_standardizer.save_standardized_data(standardized_city_24h_df, "standardized_city_24h_data")
else:
    print("未找到城市24小时数据")


## 5. 创建区域映射表

创建区域映射表，用于关联不同数据源中的区域信息。


# 创建区域映射表
region_mapping_df = weather_merger.create_region_mapping_table()

# 显示区域映射表
print(f"区域映射表形状: {region_mapping_df.shape}")
display(region_mapping_df.head(10))


## 6. 合并天气数据

合并区域天气数据和城市24小时数据。


# 合并天气数据
merged_weather_df = weather_merger.merge_all_weather_data()

# 显示合并后的数据基本信息
print(f"合并后的天气数据形状: {merged_weather_df.shape}")
print(f"列名: {merged_weather_df.columns.tolist()}")

# 查看数据前几行
merged_weather_df.head()


# 保存合并后的天气数据
data_standardizer.save_standardized_data(merged_weather_df, "merged_weather_data")


## 7. 创建分析用大宽表

将负荷数据和天气数据合并为一个大宽表，用于后续分析。


class DataMerger:
    """数据合并器类，用于创建分析用大宽表"""
    
    def __init__(self, data_root: str = "."):
        """
        初始化数据合并器
        
        Args:
            data_root: 数据根目录
        """
        self.data_root = Path(data_root)
        self.processed_dir = self.data_root / "data" / "processed"
        self.processed_dir.mkdir(parents=True, exist_ok=True)
    
    def load_data(self) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """
        加载标准化后的负荷数据和天气数据
        
        Returns:
            负荷数据DataFrame和天气数据DataFrame的元组
        """
        logger.info("加载标准化后的数据...")
        
        # 加载标准化后的负荷数据
        load_path = self.processed_dir / "standardized_load_data.parquet"
        if not load_path.exists():
            raise FileNotFoundError(f"标准化的负荷数据文件不存在: {load_path}")
        
        load_df = pd.read_parquet(load_path)
        logger.info(f"加载负荷数据: {load_df.shape}")
        
        # 加载合并后的天气数据
        weather_path = self.processed_dir / "merged_weather_data.parquet"
        if not weather_path.exists():
            weather_path = self.processed_dir / "standardized_weather_data.parquet"
            if not weather_path.exists():
                raise FileNotFoundError(f"天气数据文件不存在: {weather_path}")
        
        weather_df = pd.read_parquet(weather_path)
        logger.info(f"加载天气数据: {weather_df.shape}")
        
        return load_df, weather_df
    
    def create_wide_table(self, load_df: pd.DataFrame, weather_df: pd.DataFrame) -> pd.DataFrame:
        """
        创建分析用大宽表
        
        Args:
            load_df: 负荷数据DataFrame
            weather_df: 天气数据DataFrame
            
        Returns:
            合并后的大宽表DataFrame
        """
        logger.info("开始创建分析用大宽表...")
        
        # 确保时间列是datetime类型
        if 'timestamp' in load_df.columns:
            load_df['timestamp'] = pd.to_datetime(load_df['timestamp'], errors='coerce')
        
        if 'timestamp' in weather_df.columns:
            weather_df['timestamp'] = pd.to_datetime(weather_df['timestamp'], errors='coerce')
        
        # 创建用于合并的时间索引
        load_df['date'] = load_df['timestamp'].dt.date
        load_df['hour'] = load_df['timestamp'].dt.hour
        
        weather_df['date'] = weather_df['timestamp'].dt.date
        weather_df['hour'] = weather_df['timestamp'].dt.hour
        
        # 为了简化合并过程，我们假设每个负荷数据点都有一个对应的区域
        # 如果负荷数据中没有区域信息，我们可以尝试从地址或其他字段中提取
        if 'region' not in load_df.columns:
            if 'consumer_address' in load_df.columns:
                # 从地址中提取区域信息（简化示例）
                def extract_region(address):
                    if pd.isna(address):
                        return "未知"
                    
                    # 这里可以添加更复杂的区域提取逻辑
                    for region in weather_df['region'].unique():
                        if region in str(address):
                            return region
                    
                    return "未知"
                
                load_df['region'] = load_df['consumer_address'].apply(extract_region)
                logger.info(f"从地址中提取区域信息，共找到 {load_df['region'].nunique()} 个区域")
            else:
                # 如果无法提取区域信息，则使用默认区域
                load_df['region'] = "默认区域"
                logger.warning("无法提取区域信息，使用默认区域")
        
        # 合并数据
        # 方法1：基于时间和区域的左连接
        logger.info("使用左连接合并数据...")
        merged_df = pd.merge(
            load_df,
            weather_df,
            on=['date', 'hour', 'region'],
            how='left',
            suffixes=('', '_weather')
        )
        
        # 处理合并后的数据
        # 删除重复列
        duplicate_cols = [col for col in merged_df.columns if col.endswith('_weather') and col.replace('_weather', '') in merged_df.columns]
        merged_df = merged_df.drop(columns=duplicate_cols)
        
        # 处理缺失值
        # 对于缺失的天气数据，我们可以使用临近时间点的数据进行填充
        weather_cols = [col for col in weather_df.columns if col not in ['date', 'hour', 'region', 'timestamp']]
        for col in weather_cols:
            if col in merged_df.columns and merged_df[col].isnull().sum() > 0:
                # 使用前向填充和后向填充
                merged_df[col] = merged_df.groupby('region')[col].transform(lambda x: x.fillna(method='ffill').fillna(method='bfill'))
        
        logger.info(f"合并后的大宽表形状: {merged_df.shape}")
        return merged_df
    
    def save_wide_table(self, df: pd.DataFrame, filename: str = "analysis_wide_table") -> None:
        """
        保存分析用大宽表
        
        Args:
            df: 大宽表DataFrame
            filename: 输出文件名
        """
        output_path = self.processed_dir / f"{filename}.parquet"
        df.to_parquet(output_path, index=False)
        
        logger.info(f"分析用大宽表已保存至: {output_path}")
        
        # 同时保存一个CSV版本，便于其他工具使用
        csv_path = self.processed_dir / f"{filename}.csv"
        df.to_csv(csv_path, index=False)
        
        logger.info(f"分析用大宽表CSV版本已保存至: {csv_path}")

# 创建数据合并器实例
data_merger = DataMerger()


# 空调负荷柔性调控能力分析系统 - 数据标准化与合并

本notebook用于对负荷数据和天气数据进行标准化处理，并将它们合并为分析用的大宽表。


# 导入必要的库
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import os
import glob
from pathlib import Path
import warnings
import re
from datetime import datetime, timedelta

# 设置中文显示
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号
warnings.filterwarnings('ignore')


# 定义数据路径
DATA_DIR = Path("../data")
RAW_DATA_DIR = DATA_DIR / "raw"
PROCESSED_DATA_DIR = DATA_DIR / "processed"
WEATHER_DIR = Path("../天气数据")

# 确保目录存在
PROCESSED_DATA_DIR.mkdir(parents=True, exist_ok=True)

print(f"原始数据目录: {RAW_DATA_DIR}")
print(f"处理后数据目录: {PROCESSED_DATA_DIR}")
print(f"天气数据目录: {WEATHER_DIR}")


## 1. 加载已处理的数据


# 加载负荷数据
def load_data(file_path, file_type='parquet'):
    """加载数据文件"""
    try:
        if file_type == 'parquet':
            if Path(file_path).with_suffix('.parquet').exists():
                return pd.read_parquet(Path(file_path).with_suffix('.parquet'))
        elif file_type == 'csv':
            if Path(file_path).with_suffix('.csv').exists():
                return pd.read_csv(Path(file_path).with_suffix('.csv'))
        
        print(f"文件不存在: {file_path}")
        return pd.DataFrame()
    except Exception as e:
        print(f"加载数据时出错: {e}")
        return pd.DataFrame()

# 加载已处理的数据
load_df = load_data(RAW_DATA_DIR / "load_data", file_type='parquet')
weather_regional_df = load_data(PROCESSED_DATA_DIR / "weather_regional_merged", file_type='parquet')
weather_city_24h_df = load_data(PROCESSED_DATA_DIR / "weather_city_24h_merged", file_type='parquet')

# 显示数据基本信息
print("\n负荷数据:")
if not load_df.empty:
    print(f"- 维度: {load_df.shape}")
    print(f"- 列名: {load_df.columns.tolist()}")
else:
    print("- 数据为空")

print("\n区域天气数据:")
if not weather_regional_df.empty:
    print(f"- 维度: {weather_regional_df.shape}")
    print(f"- 列名: {weather_regional_df.columns.tolist()}")
else:
    print("- 数据为空")

print("\n城市24小时数据:")
if not weather_city_24h_df.empty:
    print(f"- 维度: {weather_city_24h_df.shape}")
    print(f"- 列名: {weather_city_24h_df.columns.tolist()}")
else:
    print("- 数据为空")


## 2. 数据标准化


# 标准化负荷数据
def standardize_load_data(df):
    """标准化负荷数据"""
    if df is None or df.empty:
        print("没有负荷数据可标准化")
        return pd.DataFrame()
    
    # 创建副本避免修改原始数据
    std_df = df.copy()
    
    # 标准化列名
    column_mapping = {
        'ID': 'id',
        'LOAD_DATE': 'load_date',
        'LOAD_HOUR': 'load_hour',
        'LOAD_VALUE': 'load_value',
        'REGION': 'region'
    }
    
    # 应用可用的列映射
    for old_col, new_col in column_mapping.items():
        if old_col in std_df.columns:
            std_df.rename(columns={old_col: new_col}, inplace=True)
    
    # 处理数据类型
    if 'id' in std_df.columns:
        std_df['id'] = std_df['id'].astype(str)
    
    if 'load_date' in std_df.columns:
        # 尝试转换日期格式
        try:
            if std_df['load_date'].dtype == 'object':
                std_df['load_date'] = pd.to_datetime(std_df['load_date'], errors='coerce')
        except Exception as e:
            print(f"转换日期格式时出错: {e}")
    
    # 创建完整的时间戳列
    if 'load_date' in std_df.columns and 'load_hour' in std_df.columns:
        try:
            std_df['timestamp'] = std_df.apply(
                lambda row: row['load_date'] + timedelta(hours=int(row['load_hour'])) 
                if pd.notnull(row['load_date']) and pd.notnull(row['load_hour']) else pd.NaT, 
                axis=1
            )
        except Exception as e:
            print(f"创建时间戳时出错: {e}")
    
    return std_df

# 标准化区域天气数据
def standardize_regional_weather(df):
    """标准化区域天气数据"""
    if df is None or df.empty:
        print("没有区域天气数据可标准化")
        return pd.DataFrame()
    
    # 创建副本避免修改原始数据
    std_df = df.copy()
    
    # 处理时间格式
    if 'time' in std_df.columns:
        try:
            if std_df['time'].dtype == 'object':
                std_df['time'] = pd.to_datetime(std_df['time'], errors='coerce')
        except Exception as e:
            print(f"转换时间格式时出错: {e}")
    
    # 确保数值列为数值类型
    numeric_cols = ['temperature', 'humidity', 'wind_speed', 'precipitation', 'pressure']
    for col in numeric_cols:
        if col in std_df.columns:
            try:
                std_df[col] = pd.to_numeric(std_df[col], errors='coerce')
            except Exception as e:
                print(f"转换列 {col} 为数值类型时出错: {e}")
    
    return std_df

# 标准化城市24小时数据
def standardize_city_24h_weather(df):
    """标准化城市24小时数据"""
    if df is None or df.empty:
        print("没有城市24小时数据可标准化")
        return pd.DataFrame()
    
    # 创建副本避免修改原始数据
    std_df = df.copy()
    
    # 处理日期格式
    if 'date' in std_df.columns:
        try:
            if std_df['date'].dtype == 'object':
                std_df['date'] = pd.to_datetime(std_df['date'], errors='coerce')
        except Exception as e:
            print(f"转换日期格式时出错: {e}")
    
    # 确保数值列为数值类型
    numeric_cols = ['aqi', 'pm25', 'pm10', 'so2', 'no2', 'o3', 'co']
    for col in numeric_cols:
        if col in std_df.columns:
            try:
                std_df[col] = pd.to_numeric(std_df[col], errors='coerce')
            except Exception as e:
                print(f"转换列 {col} 为数值类型时出错: {e}")
    
    return std_df

# 执行标准化
std_load_df = standardize_load_data(load_df)
std_weather_regional_df = standardize_regional_weather(weather_regional_df)
std_weather_city_24h_df = standardize_city_24h_weather(weather_city_24h_df)

# 显示标准化后的数据
print("\n标准化后的负荷数据:")
if not std_load_df.empty:
    print(f"- 维度: {std_load_df.shape}")
    print(f"- 列名: {std_load_df.columns.tolist()}")
    display(std_load_df.head())
else:
    print("- 数据为空")

print("\n标准化后的区域天气数据:")
if not std_weather_regional_df.empty:
    print(f"- 维度: {std_weather_regional_df.shape}")
    print(f"- 列名: {std_weather_regional_df.columns.tolist()}")
    display(std_weather_regional_df.head())
else:
    print("- 数据为空")

print("\n标准化后的城市24小时数据:")
if not std_weather_city_24h_df.empty:
    print(f"- 维度: {std_weather_city_24h_df.shape}")
    print(f"- 列名: {std_weather_city_24h_df.columns.tolist()}")
    display(std_weather_city_24h_df.head())
else:
    print("- 数据为空")


## 3. 区域映射表准备


# 创建区域映射表
def create_region_mapping():
    """创建区域映射表"""
    # 从负荷数据中提取区域信息
    load_regions = []
    if not std_load_df.empty and 'region' in std_load_df.columns:
        load_regions = std_load_df['region'].unique().tolist()
    
    # 从天气数据中提取区域信息
    weather_regions = []
    if not std_weather_regional_df.empty and 'region' in std_weather_regional_df.columns:
        weather_regions = std_weather_regional_df['region'].unique().tolist()
    
    # 从城市24小时数据中提取城市信息
    cities = []
    if not std_weather_city_24h_df.empty and 'city' in std_weather_city_24h_df.columns:
        cities = std_weather_city_24h_df['city'].unique().tolist()
    
    print(f"负荷数据区域数量: {len(load_regions)}")
    print(f"天气数据区域数量: {len(weather_regions)}")
    print(f"城市24小时数据城市数量: {len(cities)}")
    
    # 创建映射表
    mapping_data = []
    
    # 添加负荷区域
    for region in load_regions:
        mapping_data.append({
            'load_region': region,
            'weather_region': None,
            'city': None
        })
    
    # 手动映射示例 (实际应用中需要根据具体数据调整)
    # 这里仅作为示例，实际应用中需要根据具体区域名称进行匹配
    manual_mapping = [
        {'load_region': '成都市', 'weather_region': '成华', 'city': '成都'},
        {'load_region': '锦江区', 'weather_region': '锦江', 'city': '成都'},
        {'load_region': '青羊区', 'weather_region': '青羊区', 'city': '成都'},
        {'load_region': '金牛区', 'weather_region': '金牛', 'city': '成都'},
        {'load_region': '武侯区', 'weather_region': '武侯', 'city': '成都'},
        {'load_region': '成华区', 'weather_region': '成华', 'city': '成都'},
        {'load_region': '龙泉驿区', 'weather_region': '龙泉驿', 'city': '成都'},
        {'load_region': '青白江区', 'weather_region': '青白江', 'city': '成都'},
        {'load_region': '新都区', 'weather_region': '新都', 'city': '成都'},
        {'load_region': '温江区', 'weather_region': '温江', 'city': '成都'},
        {'load_region': '双流区', 'weather_region': '双流', 'city': '成都'},
        {'load_region': '郫都区', 'weather_region': '郫都', 'city': '成都'},
        {'load_region': '金堂县', 'weather_region': '金堂县', 'city': '成都'},
        {'load_region': '大邑县', 'weather_region': '大邑', 'city': '成都'},
        {'load_region': '蒲江县', 'weather_region': '蒲江', 'city': '成都'},
        {'load_region': '新津县', 'weather_region': '新津', 'city': '成都'},
        {'load_region': '都江堰市', 'weather_region': '都江堰', 'city': '成都'},
        {'load_region': '彭州市', 'weather_region': '彭州', 'city': '成都'},
        {'load_region': '崇州市', 'weather_region': '崇州', 'city': '成都'},
        {'load_region': '邛崃市', 'weather_region': '邛崃', 'city': '成都'},
        {'load_region': '简阳市', 'weather_region': '简阳', 'city': '成都'},
        {'load_region': '攀枝花市', 'weather_region': '攀枝花', 'city': '攀枝花'},
        {'load_region': '甘孜州', 'weather_region': '甘孜', 'city': '甘孜'}
    ]
    
    # 创建映射DataFrame
    mapping_df = pd.DataFrame(manual_mapping)
    
    return mapping_df

# 创建区域映射表
region_mapping_df = create_region_mapping()
print("\n区域映射表:")
display(region_mapping_df)


## 4. 数据合并


# 合并负荷数据和区域天气数据
def merge_load_and_regional_weather(load_df, weather_df, mapping_df):
    """合并负荷数据和区域天气数据"""
    if load_df.empty or weather_df.empty or mapping_df.empty:
        print("数据为空，无法合并")
        return pd.DataFrame()
    
    print("开始合并负荷数据和区域天气数据...")
    
    # 确保时间列是datetime类型
    if 'timestamp' in load_df.columns and load_df['timestamp'].dtype != 'datetime64[ns]':
        load_df['timestamp'] = pd.to_datetime(load_df['timestamp'], errors='coerce')
    
    if 'time' in weather_df.columns and weather_df['time'].dtype != 'datetime64[ns]':
        weather_df['time'] = pd.to_datetime(weather_df['time'], errors='coerce')
    
    # 创建副本避免修改原始数据
    load_copy = load_df.copy()
    weather_copy = weather_df.copy()
    
    # 使用映射表匹配区域
    merged_dfs = []
    
    for _, row in mapping_df.iterrows():
        load_region = row['load_region']
        weather_region = row['weather_region']
        
        if pd.isna(weather_region) or weather_region is None:
            continue
        
        # 筛选对应区域的数据
        load_subset = load_copy[load_copy['region'] == load_region]
        weather_subset = weather_copy[weather_copy['region'] == weather_region]
        
        if load_subset.empty or weather_subset.empty:
            print(f"区域 {load_region} 或 {weather_region} 没有数据，跳过")
            continue
        
        print(f"处理区域: {load_region} - {weather_region}")
        print(f"  负荷数据: {len(load_subset)} 行")
        print(f"  天气数据: {len(weather_subset)} 行")
        
        # 按时间合并
        # 使用最近时间点匹配
        merged_data = []
        
        for _, load_row in load_subset.iterrows():
            timestamp = load_row['timestamp']
            if pd.isna(timestamp):
                continue
            
            # 找到最接近的天气数据时间点
            closest_idx = (weather_subset['time'] - timestamp).abs().idxmin()
            weather_row = weather_subset.loc[closest_idx]
            
            # 合并数据
            merged_row = load_row.to_dict()
            
            # 添加天气数据，避免列名冲突
            for col, val in weather_row.items():
                if col not in merged_row:
                    merged_row[f'weather_{col}'] = val
                else:
                    merged_row[f'weather_{col}'] = val
            
            merged_data.append(merged_row)
        
        if merged_data:
            merged_df = pd.DataFrame(merged_data)
            merged_dfs.append(merged_df)
            print(f"  合并后: {len(merged_df)} 行")
        else:
            print("  没有匹配的数据")
    
    # 合并所有区域的结果
    if merged_dfs:
        final_df = pd.concat(merged_dfs, ignore_index=True)
        print(f"合并完成，共 {len(final_df)} 行")
        return final_df
    else:
        print("没有成功合并的数据")
        return pd.DataFrame()

# 合并负荷数据和城市24小时数据
def merge_load_and_city_24h(load_df, city_24h_df, mapping_df):
    """合并负荷数据和城市24小时数据"""
    if load_df.empty or city_24h_df.empty or mapping_df.empty:
        print("数据为空，无法合并")
        return pd.DataFrame()
    
    print("开始合并负荷数据和城市24小时数据...")
    
    # 确保时间列是datetime类型
    if 'timestamp' in load_df.columns and load_df['timestamp'].dtype != 'datetime64[ns]':
        load_df['timestamp'] = pd.to_datetime(load_df['timestamp'], errors='coerce')
    
    if 'date' in city_24h_df.columns and city_24h_df['date'].dtype != 'datetime64[ns]':
        city_24h_df['date'] = pd.to_datetime(city_24h_df['date'], errors='coerce')
    
    # 创建副本避免修改原始数据
    load_copy = load_df.copy()
    city_24h_copy = city_24h_df.copy()
    
    # 使用映射表匹配区域和城市
    merged_dfs = []
    
    for _, row in mapping_df.iterrows():
        load_region = row['load_region']
        city = row['city']
        
        if pd.isna(city) or city is None:
            continue
        
        # 筛选对应区域和城市的数据
        load_subset = load_copy[load_copy['region'] == load_region]
        city_subset = city_24h_copy[city_24h_copy['city'] == city]
        
        if load_subset.empty or city_subset.empty:
            print(f"区域 {load_region} 或城市 {city} 没有数据，跳过")
            continue
        
        print(f"处理区域/城市: {load_region} - {city}")
        print(f"  负荷数据: {len(load_subset)} 行")
        print(f"  城市24小时数据: {len(city_subset)} 行")
        
        # 按日期合并
        # 为负荷数据提取日期部分
        load_subset['date'] = load_subset['timestamp'].dt.date
        
        # 将city_subset的date转换为日期
        city_subset['date'] = city_subset['date'].dt.date
        
        # 按日期合并
        merged_df = pd.merge(
            load_subset,
            city_subset,
            on='date',
            how='left',
            suffixes=('', '_city')
        )
        
        if not merged_df.empty:
            merged_dfs.append(merged_df)
            print(f"  合并后: {len(merged_df)} 行")
        else:
            print("  没有匹配的数据")
    
    # 合并所有区域的结果
    if merged_dfs:
        final_df = pd.concat(merged_dfs, ignore_index=True)
        print(f"合并完成，共 {len(final_df)} 行")
        return final_df
    else:
        print("没有成功合并的数据")
        return pd.DataFrame()

# 执行合并
# 注意：这里只是示例，实际运行可能需要较长时间
print("是否运行数据合并？(可能需要较长时间)")
print("如需运行，请取消下面代码的注释")

# 合并负荷数据和区域天气数据
# merged_load_regional_df = merge_load_and_regional_weather(std_load_df, std_weather_regional_df, region_mapping_df)

# 合并负荷数据和城市24小时数据
# merged_load_city_24h_df = merge_load_and_city_24h(std_load_df, std_weather_city_24h_df, region_mapping_df)


## 5. 保存合并后的数据


# 保存合并后的数据
def save_merged_data(df, name):
    """保存合并后的数据"""
    if df is None or df.empty:
        print(f"没有数据可保存: {name}")
        return
    
    # 创建保存路径
    csv_path = PROCESSED_DATA_DIR / f"{name}.csv"
    parquet_path = PROCESSED_DATA_DIR / f"{name}.parquet"
    
    try:
        # 保存为CSV
        df.to_csv(csv_path, index=False, encoding='utf-8')
        print(f"已保存CSV: {csv_path}")
        
        # 保存为Parquet
        df.to_parquet(parquet_path, index=False)
        print(f"已保存Parquet: {parquet_path}")
    except Exception as e:
        print(f"保存数据时出错: {e}")

# 保存合并后的数据
# 注意：取消注释以保存数据
# if 'merged_load_regional_df' in locals() and not merged_load_regional_df.empty:
#     save_merged_data(merged_load_regional_df, "load_weather_regional_merged")
    
# if 'merged_load_city_24h_df' in locals() and not merged_load_city_24h_df.empty:
#     save_merged_data(merged_load_city_24h_df, "load_weather_city_24h_merged")


## 6. 后续步骤


"""
完成数据标准化与合并后，后续步骤包括：

1. **特征工程**
   - 创建特征工程notebook
   - 提取时间特征（小时、日、周、季节等）
   - 计算温度敏感度指标
   - 构建滞后特征（如前一天负荷、前一周同期负荷等）
   - 创建空调负荷识别特征

2. **模型构建**
   - 创建模型构建notebook
   - 空调负荷识别模型
   - 柔性调控能力评估模型
   - 模型验证与调优

3. **结果可视化与报告**
   - 创建可视化与报告notebook
   - 空调负荷时空分布可视化
   - 柔性调控能力评估报告
   - 决策支持仪表板
"""

