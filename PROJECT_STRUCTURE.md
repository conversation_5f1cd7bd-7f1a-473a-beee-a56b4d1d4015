# 空调负荷柔性调控能力分析系统 - 项目结构说明

## 项目概述

本项目实现了一个完整的空调负荷柔性调控能力分析系统，包含数据处理、模型训练、预测分析等功能。

## 核心文件结构

### 📁 主要脚本文件

#### 1. 数据处理脚本
- **`create_final_wide_table.py`** ⭐ **[核心文件]**
  - 最终版大宽表生成脚本
  - 整合负荷数据、气象数据、空气质量数据
  - 支持完整数据集（35万条记录）处理
  - 使用方法：`python create_final_wide_table.py`

#### 2. 模型训练与预测
- **`ac_load_prediction.py`** ⭐ **[核心文件]**
  - 空调负荷预测与柔性调控评估脚本
  - 基于训练好的模型进行预测
  - 生成分析报告和可视化图表
  - 使用方法：`python ac_load_prediction.py`

### 📁 数据文件

#### 原始数据
- **`complete_load_analysis_data.csv`** (179MB)
  - 完整的负荷分析数据，350,880条记录
  - 包含用户地址、负荷指标、时间戳等信息

- **`天气数据/`** 目录
  - **气象数据**：68个xls文件，按区域和年份命名
    - 格式：`{区域名}{年份}.xls`（如：简阳23.xls）
    - 包含温度、湿度、气压、降水、风速等指标
  - **空气质量数据**：2个xlsx文件
    - `城市24小时-2023.xlsx`：2023年21个城市空气质量数据
    - `城市24小时-24-25.xlsx`：2024-2025年空气质量数据

#### 处理后数据
- **`data/processed/final_wide_table_*.csv`** (215MB)
  - 最终生成的大宽表，350,880行×127列
  - 整合了负荷、气象、空气质量数据
  - 包含30个气象特征和42个空气质量特征

### 📁 模型文件

- **`models/`** 目录
  - `ac_load_model.pkl`：训练好的空调负荷识别模型
  - `ac_load_scaler.pkl`：数据预处理器
  - `ac_load_features.json`：特征配置和模型信息

### 📁 结果文件

- **`results/`** 目录
  - `ac_load_analysis_report.txt`：空调负荷分析报告
  - `data_exploration_report.txt`：数据探索报告

- **`figures/`** 目录
  - `ac_load_analysis.png`：空调负荷分析可视化图表
  - `correlation_analysis.png`：特征相关性分析图

### 📁 配置文件

- **`config/`** 目录
  - `data_config.py`：数据配置文件
  - `data_schema.md`：数据结构说明

### 📁 文档文件

- **`README.md`**：项目总体说明
- **`技术方案.md`**：技术方案详细说明
- **`project_summary.md`**：项目总结报告
- **`PROJECT_STRUCTURE.md`**：本文件，项目结构说明
- **`requirements.txt`**：Python依赖包列表

### 📁 Jupyter Notebooks

- **`notebooks/`** 目录
  - `01_数据加载与探索.ipynb`：数据加载和探索分析
  - `02_数据标准化与合并.ipynb`：数据标准化和合并
  - `03_特征工程.ipynb`：特征工程
  - `04_模型构建.ipynb`：模型构建和训练
  - `05_结果可视化与报告.ipynb`：结果可视化

### 📁 日志文件

- **`logs/`** 目录
  - 各种处理过程的日志文件
  - 用于调试和监控数据处理进度

## 使用流程

### 1. 环境准备
```bash
# 安装依赖
pip install -r requirements.txt
```

### 2. 生成大宽表
```bash
# 生成完整大宽表（35万条记录）
python create_final_wide_table.py
# 按提示输入样本大小，直接回车加载全部数据
```

### 3. 模型训练（可选）
```bash
# 如果需要重新训练模型
python create_final_wide_table.py train
```

### 4. 预测分析
```bash
# 进行空调负荷预测和柔性调控评估
python ac_load_prediction.py
```

## 数据流程

```
原始数据
├── complete_load_analysis_data.csv (负荷数据)
├── 天气数据/*.xls (气象数据)
└── 天气数据/城市24小时*.xlsx (空气质量数据)
    ↓
数据处理 (create_final_wide_table.py)
    ↓
大宽表 (final_wide_table_*.csv)
    ↓
模型训练/预测 (ac_load_prediction.py)
    ↓
结果输出
├── 分析报告 (results/*.txt)
├── 可视化图表 (figures/*.png)
└── 模型文件 (models/*.pkl)
```

## 关键技术特点

1. **多源数据融合**：整合负荷、气象、空气质量三类数据
2. **大规模数据处理**：支持35万条记录的高效处理
3. **时空数据匹配**：基于时间和地理位置的精确匹配
4. **机器学习建模**：空调负荷识别和柔性调控评估
5. **完整分析流程**：从数据处理到结果输出的端到端流程

## 维护说明

### 核心文件（不可删除）
- `create_final_wide_table.py`
- `ac_load_prediction.py`
- `complete_load_analysis_data.csv`
- `天气数据/` 目录
- `models/` 目录
- `requirements.txt`

### 可选文件
- `notebooks/` 目录：用于交互式分析
- `logs/` 目录：调试和监控用
- `figures/` 和 `results/` 目录：输出结果

### 临时文件（可删除）
- `__pycache__/` 目录
- `*.pyc` 文件
- 旧版本的脚本文件

## 扩展建议

1. **增加更多区域**：扩展地址-区域映射表
2. **实时数据接入**：开发实时数据处理接口
3. **模型优化**：尝试深度学习模型
4. **Web界面**：开发用户友好的Web界面
5. **API服务**：提供RESTful API服务

---

**维护者**: AI Assistant  
**最后更新**: 2025-07-14  
**版本**: v1.0
